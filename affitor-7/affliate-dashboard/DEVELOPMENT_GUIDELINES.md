# Development Guidelines - Affiliate Dashboard

## Project Overview
This is a Next.js 15 TypeScript project with Redux Toolkit + Redux Saga for state management, Strapi as the backend CMS, and a comprehensive affiliate dashboard system.

## Architecture Patterns

### 1. Project Structure
```
src/
├── app/                    # Next.js 15 App Router (if used)
├── components/            # Reusable UI components
│   ├── ui/               # Shadcn/ui components
│   └── [ComponentName].tsx
├── containers/           # Page-level containers with business logic
├── features/            # Redux slices and sagas (by domain)
│   ├── [domain]/
│   │   ├── [domain].slice.ts
│   │   └── [domain].saga.ts
├── pages/               # Next.js pages (Pages Router)
│   ├── api/            # API routes (proxy to Strapi)
│   └── [page].tsx
├── interfaces/          # TypeScript type definitions
├── utils/              # Utility functions and API clients
└── styles/             # Global styles
```

### 2. State Management Pattern (Redux Toolkit + Saga)

#### Feature Slice Structure
```typescript
// src/features/[domain]/[domain].slice.ts
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface [Domain]State {
  data: [Domain]Item[];
  meta: Meta | null;
  loading: boolean;
  error: string | null;
  success: string | null;
}

const initialState: [Domain]State = {
  data: [],
  meta: null,
  loading: false,
  error: null,
  success: null,
};

const [domain]Slice = createSlice({
  name: "[domain]",
  initialState,
  reducers: {
    // Request actions
    fetch[Domain]Request: (state, action: PayloadAction<QueryParams>) => {
      state.loading = true;
      state.error = null;
    },
    // Success actions
    fetch[Domain]Success: (state, action: PayloadAction<Response>) => {
      state.loading = false;
      state.data = action.payload.data;
      state.meta = action.payload.meta;
      state.error = null;
    },
    // Failure actions
    fetch[Domain]Failure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    // Clear actions
    clear[Domain]State: (state) => {
      return initialState;
    },
  },
});

export const { actions, reducer } = [domain]Slice;

// Selectors
export const select[Domain]Data = (state: any) => state.[domain].data;
export const select[Domain]Loading = (state: any) => state.[domain].loading;
export const select[Domain]Error = (state: any) => state.[domain].error;
```

#### Saga Pattern
```typescript
// src/features/[domain]/[domain].saga.ts
import { call, put, takeEvery } from "redux-saga/effects";
import { actions } from "./[domain].slice";
import { PayloadAction } from "@reduxjs/toolkit";

function* fetch[Domain]Saga(action: PayloadAction<QueryParams>): Generator<any, void, any> {
  try {
    const token = typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;
    
    if (!token) {
      yield put(actions.fetch[Domain]Failure("Authentication required"));
      return;
    }

    const response = yield call(fetch, `/api/[endpoint]?${buildQuery(action.payload)}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = yield response.json();
      yield put(actions.fetch[Domain]Failure(errorData.error || "Failed to fetch data"));
      return;
    }

    const data = yield response.json();
    yield put(actions.fetch[Domain]Success(data));
  } catch (error: any) {
    yield put(actions.fetch[Domain]Failure(error.message || "Failed to fetch data"));
  }
}

export default function* [domain]Saga() {
  yield takeEvery(actions.fetch[Domain]Request.type, fetch[Domain]Saga);
}
```

### 3. API Layer Pattern

#### API Route Structure (Next.js API Routes)
```typescript
// src/pages/api/[endpoint]/index.ts
import { NextApiRequest, NextApiResponse } from "next";
import { StrapiAdminClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;

  if (method === "GET") {
    return handleGet(req, res);
  } else if (method === "POST") {
    return handlePost(req, res);
  } else {
    return res.status(405).json({ error: "Method not allowed" });
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    // Extract and validate query parameters
    const { page, pageSize, search, status } = req.query;
    
    const query = {
      page: page ? parseInt(page as string) : 1,
      pageSize: pageSize ? parseInt(pageSize as string) : 10,
      search: search as string | undefined,
      status: status as string | undefined,
    };

    // Call Strapi client
    const data = await StrapiAdminClient.get[Domain](query, token!);

    // Transform data if needed
    const transformedData = transformResponse(data);

    res.status(200).json(transformedData);
  } catch (error: any) {
    console.error("API error:", error);
    sendApiError(res, error, "Error fetching data");
  }
}
```

#### Strapi Client Pattern
```typescript
// Add to src/utils/request.ts StrapiAdminClient
get[Domain]: async function (
  query: {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
  },
  token: string
) {
  try {
    if (!token) {
      throw new Error("Admin authentication required");
    }

    // Build Strapi query parameters
    const queryParams = new URLSearchParams({
      page: (query.page || 1).toString(),
      pageSize: (query.pageSize || 10).toString(),
      sort: "id:ASC",
    });

    // Add population
    queryParams.append("populate[relation]", "*");

    // Add filters
    if (query.status) {
      queryParams.append("filters[status][$eq]", query.status);
    }

    if (query.search) {
      queryParams.append("filters[$or][0][field][$containsi]", query.search);
    }

    return StrapiAdminClient.client.get(
      `/content-manager/collection-types/api::[domain].[domain]?${queryParams}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
  } catch (error: any) {
    console.error("Get [domain] error:", error);
    throw error;
  }
},
```

### 4. Component Patterns

#### Container Component Pattern
```typescript
// src/containers/[Domain]/[DomainList].tsx
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { actions, select[Domain]Data, select[Domain]Loading } from "@/features/[domain]/[domain].slice";

const [Domain]List: React.FC = () => {
  const dispatch = useDispatch();
  const data = useSelector(select[Domain]Data);
  const loading = useSelector(select[Domain]Loading);

  useEffect(() => {
    dispatch(actions.fetch[Domain]Request({ page: 1, pageSize: 10 }));
  }, [dispatch]);

  return (
    <div>
      {/* Component JSX */}
    </div>
  );
};

export default [Domain]List;
```

#### UI Component Pattern (Shadcn/ui)
```typescript
// src/components/ui/[component].tsx
import * as React from "react";
import { cn } from "@/lib/utils";

interface [Component]Props extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "secondary";
  size?: "sm" | "md" | "lg";
}

const [Component] = React.forwardRef<HTMLDivElement, [Component]Props>(
  ({ className, variant = "default", size = "md", ...props }, ref) => {
    return (
      <div
        className={cn(
          "base-classes",
          {
            "variant-classes": variant === "default",
            "size-classes": size === "md",
          },
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);

[Component].displayName = "[Component]";

export { [Component] };
```

### 5. TypeScript Patterns

#### Interface Definitions
```typescript
// src/interfaces/index.ts
export interface I[Domain] {
  id: number;
  documentId: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  // Domain-specific fields
  field1: string;
  field2: number;
  // Relations
  relation?: {
    id: number;
    name: string;
  };
}

export interface [Domain]Response {
  data: I[Domain][];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}
```

## Coding Standards

### 1. Naming Conventions
- **Files**: PascalCase for components, camelCase for utilities
- **Components**: PascalCase (e.g., `PayoutTable.tsx`)
- **Functions**: camelCase (e.g., `fetchPayouts`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_BASE_URL`)
- **Interfaces**: PascalCase with `I` prefix (e.g., `IPayout`)

### 2. Import Organization
```typescript
// 1. React imports
import React, { useEffect, useState } from "react";

// 2. Third-party libraries
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";

// 3. Internal imports (absolute paths)
import { Button } from "@/components/ui/button";
import { actions } from "@/features/payout/payout.slice";

// 4. Relative imports
import "./styles.css";
```

### 3. Error Handling
```typescript
// API Error Handling
try {
  const response = await apiCall();
  return response.data;
} catch (error: any) {
  console.error("Specific operation error:", error);
  throw new Error(error.response?.data?.error || error.message || "Operation failed");
}

// Component Error Boundaries
// Use React Error Boundaries for component-level error handling
```

### 4. Authentication Patterns
```typescript
// Token Management
const token = typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;
const adminToken = typeof window !== "undefined" ? localStorage.getItem("admin_token") : null;

// API Context Middleware
const { token } = createApiContext(req, { requireAuth: true });
```

## Best Practices

### 1. State Management
- Use Redux Toolkit for global state
- Keep component state local when possible
- Use selectors for derived state
- Implement loading and error states consistently

### 2. API Design
- Use Next.js API routes as proxies to Strapi
- Implement consistent error handling
- Transform Strapi responses to match frontend expectations
- Use TypeScript for request/response types

### 3. Component Design
- Follow single responsibility principle
- Use composition over inheritance
- Implement proper TypeScript typing
- Use Shadcn/ui components as base

### 4. Performance
- Implement proper memoization with React.memo
- Use useCallback and useMemo appropriately
- Implement pagination for large datasets
- Optimize bundle size with proper imports

### 5. Testing
- Write unit tests for utility functions
- Test Redux slices and sagas
- Implement integration tests for API routes
- Use React Testing Library for component tests

## Package Management
- **Always use yarn** for dependency management
- Never manually edit package.json
- Use `yarn add` for dependencies, `yarn add -D` for dev dependencies
- Run `yarn install` after pulling changes

## Environment Configuration
- Use `.env.local` for local development
- Follow the `.env.example` template
- Never commit sensitive environment variables
- Use `NEXT_PUBLIC_` prefix for client-side variables

## Request/Response Patterns

### 1. API Request Structure
```typescript
// Query Parameters
interface QueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sort?: string;
  filters?: Record<string, any>;
}

// Request Body (POST/PUT)
interface RequestBody {
  data: Record<string, any>;
  // Additional metadata if needed
}
```

### 2. API Response Structure
```typescript
// Standard Response Format
interface ApiResponse<T> {
  data: T;
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
  error?: string;
  message?: string;
}

// Error Response Format
interface ErrorResponse {
  error: string;
  statusCode: number;
  details?: any;
}
```

### 3. Strapi Integration Patterns
```typescript
// Strapi Query Building
const buildStrapiQuery = (params: QueryParams) => {
  const query = new URLSearchParams();

  // Pagination
  query.append("pagination[page]", (params.page || 1).toString());
  query.append("pagination[pageSize]", (params.pageSize || 10).toString());

  // Population
  query.append("populate[relation][populate][nested]", "*");

  // Filtering
  if (params.search) {
    query.append("filters[$or][0][field][$containsi]", params.search);
  }

  // Sorting
  if (params.sort) {
    query.append("sort", params.sort);
  }

  return query.toString();
};

// Strapi Response Transformation
const transformStrapiResponse = (strapiData: any) => {
  return {
    data: strapiData.results?.map(transformItem) || [],
    meta: {
      pagination: strapiData.pagination || {
        page: 1,
        pageSize: 10,
        pageCount: 0,
        total: 0,
      },
    },
  };
};
```

## Form Handling Patterns

### 1. React Hook Form Integration
```typescript
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

// Validation Schema
const schema = yup.object({
  field1: yup.string().required("Field 1 is required"),
  field2: yup.number().positive("Must be positive").required(),
});

// Form Component
const FormComponent: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      field1: "",
      field2: 0,
    },
  });

  const onSubmit = async (data: FormData) => {
    try {
      await submitData(data);
      reset();
    } catch (error) {
      // Handle error
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <input {...register("field1")} />
      {errors.field1 && <span>{errors.field1.message}</span>}

      <button type="submit" disabled={isSubmitting}>
        Submit
      </button>
    </form>
  );
};
```

### 2. Form Validation Patterns
```typescript
// Reusable validation schemas
export const commonValidations = {
  email: yup.string().email("Invalid email").required("Email is required"),
  password: yup.string().min(8, "Password must be at least 8 characters"),
  amount: yup.number().positive("Amount must be positive").required(),
  documentId: yup.string().required("Document ID is required"),
};

// Compose schemas
const payoutSchema = yup.object({
  amount: commonValidations.amount,
  method: yup.string().oneOf(["paypal", "bank_transfer"]).required(),
  partnerId: commonValidations.documentId,
});
```

## Data Transformation Patterns

### 1. Strapi to Frontend Transformation
```typescript
// Transform Strapi payout data to frontend format
const transformPayoutData = (strapiPayout: any) => ({
  id: strapiPayout.id,
  documentId: strapiPayout.documentId,
  amount: strapiPayout.amount,
  status: strapiPayout.payout_status, // Map field names
  method: strapiPayout.method,
  createdAt: strapiPayout.createdAt,
  // Transform nested relations
  partner: strapiPayout.referrer ? {
    id: strapiPayout.referrer.id,
    name: strapiPayout.referrer.user?.username || strapiPayout.referrer.referral_code,
    email: strapiPayout.referrer.user?.email,
  } : null,
});
```

### 2. Frontend to API Transformation
```typescript
// Transform frontend form data to API format
const transformFormToApi = (formData: FormData) => ({
  payout_status: formData.status, // Map field names back
  method: formData.method,
  amount: formData.amount,
  referrer: {
    connect: [{ documentId: formData.partnerId }],
  },
});
```

## Security Patterns

### 1. Authentication
```typescript
// Token validation middleware
export const requireAuth = (handler: NextApiHandler) => {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const token = req.headers.authorization?.replace("Bearer ", "");

    if (!token) {
      return res.status(401).json({ error: "Authentication required" });
    }

    try {
      // Validate token with Strapi
      await validateToken(token);
      return handler(req, res);
    } catch (error) {
      return res.status(401).json({ error: "Invalid token" });
    }
  };
};
```

### 2. Input Validation
```typescript
// Server-side validation
const validateInput = (schema: yup.Schema) => {
  return async (req: NextApiRequest, res: NextApiResponse, next: Function) => {
    try {
      await schema.validate(req.body);
      next();
    } catch (error: any) {
      return res.status(400).json({ error: error.message });
    }
  };
};
```

## Testing Patterns

### 1. Redux Testing
```typescript
// Test Redux slice
describe("payoutSlice", () => {
  it("should handle fetchPayoutsRequest", () => {
    const initialState = { loading: false, error: null };
    const action = actions.fetchPayoutsRequest({ page: 1 });
    const state = reducer(initialState, action);

    expect(state.loading).toBe(true);
    expect(state.error).toBe(null);
  });
});

// Test Saga
describe("payoutSaga", () => {
  it("should fetch payouts successfully", () => {
    const generator = fetchPayoutsSaga(actions.fetchPayoutsRequest({ page: 1 }));

    expect(generator.next().value).toEqual(
      call(fetch, expect.stringContaining("/api/payouts"))
    );
  });
});
```

### 2. Component Testing
```typescript
// Test React component
import { render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import { store } from "@/store";

describe("PayoutTable", () => {
  it("renders payout data", () => {
    render(
      <Provider store={store}>
        <PayoutTable />
      </Provider>
    );

    expect(screen.getByText("Payouts")).toBeInTheDocument();
  });
});
```

## Performance Optimization

### 1. Component Optimization
```typescript
// Memoize expensive components
const ExpensiveComponent = React.memo(({ data }: { data: any[] }) => {
  const processedData = useMemo(() => {
    return data.map(item => expensiveTransformation(item));
  }, [data]);

  return <div>{/* Render processed data */}</div>;
});

// Optimize event handlers
const ParentComponent = () => {
  const handleClick = useCallback((id: string) => {
    // Handle click
  }, []);

  return (
    <div>
      {items.map(item => (
        <ChildComponent key={item.id} onClick={handleClick} />
      ))}
    </div>
  );
};
```

### 2. Bundle Optimization
```typescript
// Dynamic imports for code splitting
const LazyComponent = dynamic(() => import("./HeavyComponent"), {
  loading: () => <div>Loading...</div>,
});

// Tree-shaking friendly imports
import { Button } from "@/components/ui/button"; // ✅ Good
import * as UI from "@/components/ui"; // ❌ Avoid
```

## Deployment Guidelines

### 1. Build Process
```bash
# Development
yarn dev

# Production build
yarn build
yarn start

# Linting
yarn lint
```

### 2. Environment Variables
```bash
# Required for production
NEXT_PUBLIC_API_BASE_URL=https://api.example.com
DATABASE_URL=postgresql://...
STRIPE_SECRET_KEY=sk_...
```

## Troubleshooting Common Issues

### 1. Redux State Issues
- Check if the reducer is properly registered in store
- Verify action types match between slice and saga
- Ensure selectors are correctly typed

### 2. API Integration Issues
- Verify Strapi endpoint URLs and authentication
- Check request/response data transformation
- Validate query parameter formatting

### 3. TypeScript Issues
- Ensure all interfaces are properly exported
- Check import paths and module resolution
- Verify generic type parameters

## Code Review Checklist

### Before Submitting PR
- [ ] All TypeScript errors resolved
- [ ] Components properly typed
- [ ] Error handling implemented
- [ ] Loading states handled
- [ ] API responses transformed correctly
- [ ] Redux actions and selectors tested
- [ ] No console.log statements in production code
- [ ] Proper import organization
- [ ] Consistent naming conventions
- [ ] Documentation updated if needed
