/**
 * Utility functions for handling links in the editor
 */

// URL validation regex
const URL_REGEX = /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&=]*)$/;

/**
 * Check if a string is a valid URL
 */
export const isValidUrl = (text: string): boolean => {
  return URL_REGEX.test(text);
};

/**
 * Extract URLs from text
 */
export const extractUrls = (text: string): string[] => {
  const urlMatches = text.match(/https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&=]*)/g);
  return urlMatches || [];
};

/**
 * Check if URL is embeddable
 */
export const isEmbeddableUrl = (url: string): boolean => {
  const embeddablePatterns = [
    /youtube\.com\/watch/,
    /youtu\.be\//,
    /vimeo\.com\//,
    /twitter\.com\//,
    /x\.com\//,
    /instagram\.com\//,
    /tiktok\.com\//,
    /codepen\.io\//,
    /codesandbox\.io\//,
    /figma\.com\//,
    /spotify\.com\//,
    /soundcloud\.com\//,
    /loom\.com\//,
    /notion\.so\//,
    /airtable\.com\//
  ];
  return embeddablePatterns.some(pattern => pattern.test(url));
};

/**
 * Get domain from URL
 */
export const getDomainFromUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname.replace('www.', '');
  } catch {
    return url;
  }
};

/**
 * Convert YouTube URL to embed URL
 */
export const getYouTubeEmbedUrl = (url: string): string | null => {
  const youtubeRegex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/;
  const match = url.match(youtubeRegex);
  if (match && match[1]) {
    return `https://www.youtube.com/embed/${match[1]}`;
  }
  return null;
};

/**
 * Convert Vimeo URL to embed URL
 */
export const getVimeoEmbedUrl = (url: string): string | null => {
  const vimeoRegex = /vimeo\.com\/(\d+)/;
  const match = url.match(vimeoRegex);
  if (match && match[1]) {
    return `https://player.vimeo.com/video/${match[1]}`;
  }
  return null;
};

/**
 * Get embed URL for supported platforms
 */
export const getEmbedUrl = (url: string): string | null => {
  // YouTube
  const youtubeEmbed = getYouTubeEmbedUrl(url);
  if (youtubeEmbed) return youtubeEmbed;

  // Vimeo
  const vimeoEmbed = getVimeoEmbedUrl(url);
  if (vimeoEmbed) return vimeoEmbed;

  // For other platforms, return the original URL
  // The embed plugin will handle the conversion
  if (isEmbeddableUrl(url)) {
    return url;
  }

  return null;
};

/**
 * Create a Yoopta block for a link
 */
export const createLinkBlock = (url: string, text?: string) => {
  const blockId = `link-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  return {
    id: blockId,
    type: 'Paragraph',
    value: [
      {
        id: `element-${blockId}`,
        type: 'paragraph',
        children: [
          {
            text: text || url,
            link: {
              url: url,
              target: '_blank',
              rel: 'noopener noreferrer'
            }
          }
        ],
        props: { nodeType: 'block' }
      }
    ],
    meta: {
      order: 0,
      depth: 0
    }
  };
};

/**
 * Create a Yoopta block for an embed
 */
export const createEmbedBlock = (url: string) => {
  const blockId = `embed-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  return {
    id: blockId,
    type: 'Embed',
    value: [
      {
        id: `element-${blockId}`,
        type: 'embed',
        props: {
          nodeType: 'void',
          src: url,
          width: 560,
          height: 315
        },
        children: [{ text: '' }]
      }
    ],
    meta: {
      order: 0,
      depth: 0
    }
  };
};

/**
 * Create a Yoopta block for a video
 */
export const createVideoBlock = (url: string) => {
  const blockId = `video-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  return {
    id: blockId,
    type: 'Video',
    value: [
      {
        id: `element-${blockId}`,
        type: 'video',
        props: {
          nodeType: 'void',
          src: url,
          width: 560,
          height: 315
        },
        children: [{ text: '' }]
      }
    ],
    meta: {
      order: 0,
      depth: 0
    }
  };
};

/**
 * Create a bookmark block (using callout for now)
 */
export const createBookmarkBlock = (url: string, title?: string, description?: string) => {
  const blockId = `bookmark-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const domain = getDomainFromUrl(url);
  
  return {
    id: blockId,
    type: 'Callout',
    value: [
      {
        id: `element-${blockId}`,
        type: 'callout',
        children: [
          {
            text: title || domain,
            bold: true
          },
          { text: '\n' },
          {
            text: description || url,
            link: {
              url: url,
              target: '_blank',
              rel: 'noopener noreferrer'
            }
          }
        ],
        props: {
          nodeType: 'block',
          theme: 'info'
        }
      }
    ],
    meta: {
      order: 0,
      depth: 0
    }
  };
};

/**
 * Fetch metadata for a URL (for bookmark creation)
 */
export const fetchUrlMetadata = async (url: string): Promise<{
  title?: string;
  description?: string;
  image?: string;
}> => {
  try {
    // This would typically be done on the server side to avoid CORS issues
    // For now, we'll return basic metadata
    const domain = getDomainFromUrl(url);
    return {
      title: domain,
      description: url,
      image: undefined
    };
  } catch (error) {
    console.error('Error fetching URL metadata:', error);
    return {};
  }
};
