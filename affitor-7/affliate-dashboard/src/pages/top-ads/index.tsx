import React from "react";
import dynamic from "next/dynamic";

// Use dynamic import with SSR disabled to avoid hydration issues with hash-based UI
const TopAdsWithNoSSR = dynamic(() => import("@/containers/TopAds"), {
  ssr: false,
});

export default function TopAdsPage() {
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Top Ads</h1>
      <TopAdsWithNoSSR />
    </div>
  );
}
