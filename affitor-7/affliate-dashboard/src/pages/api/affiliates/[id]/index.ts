import { NextApiRequest, NextApiResponse } from "next";
import { AppError, IAffiliate } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";

const getAffiliateById = async (id: string): Promise<IAffiliate> => {
  const response: any = await StrapiClient.getAffiliateById(id);
  return response.data;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<IAffiliate | AppError>
) {
  if (req.method === "GET") {
	try {
	  const { id } = req.query;
	  console.log('LOG-req.query', req.query);
	  if (!id || typeof id !== "string") {
		return res.status(400).json({ statusCode: 400, message: "Invalid ID" });
	  }

	  const affiliate = await getAffiliateById(id);
	  res.status(200).json(affiliate);
	} catch (error: any) {
	  console.error(error);
	  sendApiError(res, error, "Error fetching affiliate");
	}
  } else {
	res.setHeader("Allow", ["GET"]);
	res.status(405).json({ statusCode: 405, message: `Method ${req.method} Not Allowed` });
  }
}