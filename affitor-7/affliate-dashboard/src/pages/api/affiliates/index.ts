import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { createApiContext } from "@/utils/api-middleware";
import qs from "qs";
import { sendApiError } from "@/utils/api-error-handler";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    console.time("Fetching affiliates...");

    // Use centralized context creation
    const { token, cookies } = createApiContext(req, { forwardCookies: true });

    // Serialize the query object into a query string
    const queryString = qs.stringify(req.query, { encodeValuesOnly: true });

    // Pass token and cookies to StrapiClient
    const response = await StrapiClient.getAffiliates(
      queryString,
      token,
      cookies
    );

    console.timeEnd("Fetching affiliates...");
    res.status(200).json(response);
  } catch (error: any) {
    console.error("Error fetching affiliates:", error);
    sendApiError(res, error, "Error fetching affiliates");
  }
}
