import { AppError, ISubscriptionTier } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import { createApiContext } from "@/utils/api-middleware";
import { sendApiError } from "@/utils/api-error-handler";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ data: ISubscriptionTier[]; meta: any } | AppError>
) {
  try {
    // Use centralized context creation
    const { token, cookies } = createApiContext(req, { forwardCookies: true });

    // Fetch subscription tiers data from Strapi with cookies
    const response: any = await StrapiClient.getSubscriptionTiers(
      token || undefined,
      cookies
    );

    // Return the data
    res.status(200).json(response);
  } catch (error: any) {
    console.error("Error fetching subscription tiers:", error);
    sendApiError(res, error, "Error fetching subscription tiers");
  }
}
