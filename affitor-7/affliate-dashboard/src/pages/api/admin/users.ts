import { StrapiAdminClient } from "@/utils/request";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Get authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: "Authorization header required" });
    }

    const token = authHeader.replace("Bearer ", "");

    // Extract query parameters
    const {
      page = 1,
      pageSize = 10,
      search = "",
      sort = "id:ASC"
    } = req.query;

    // Use StrapiAdminClient.getReferrals to fetch referrals as customers
    const data = await StrapiAdminClient.getReferrals({
      page: Number(page),
      pageSize: Number(pageSize),
      search: search as string,
      sort: sort as string,
    }, token) as any;

    // Transform the data to match our expected format for customers
    const transformedData = {
      data: data.results?.map((ref: any) => ({
        id: ref.id,
        documentId: ref.documentId,
        referral_status: ref.referral_status,
        total_paid: ref.total_paid,
        createdAt: ref.createdAt,
        updatedAt: ref.updatedAt,
        publishedAt: ref.publishedAt,
        user: ref.user,
        referrer: ref.referrer,
        referrer_link: ref.referrer_link,
        referral_activities_count: ref.referral_activities?.count ?? 0,
        referral_commissions_count: ref.referral_commissions?.count ?? 0,
        status: ref.status,
      })) || [],
      meta: {
        pagination: data.pagination || {
          page: parseInt(page.toString()),
          pageSize: parseInt(pageSize.toString()),
          pageCount: 0,
          total: 0,
        },
      },
    };

    res.status(200).json(transformedData);
  } catch (error: any) {
    console.error("API Error:", error);
    res.status(500).json({
      error: "Failed to fetch users",
      details: error.message,
    });
  }
}