import { AppError } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import type { NextApiRequest, NextApiResponse } from "next";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export interface Prompt {
  id: number;
  documentId: string;
  title: string;
  content: string;
  description: string | null;
  is_default: boolean;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
}

export interface PromptsResponse {
  data: Prompt[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PromptsResponse | AppError>
) {
  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    // Call Strapi API to get prompts
    const response: any = await StrapiClient.client.get("/api/prompts", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    // Return response data (consistent with other API endpoints)
    res.status(200).json(response.data);
  } catch (error: any) {
    console.error("Error in prompts API route:", error);
    sendApiError(res, error, "Error fetching prompts");
  }
}
