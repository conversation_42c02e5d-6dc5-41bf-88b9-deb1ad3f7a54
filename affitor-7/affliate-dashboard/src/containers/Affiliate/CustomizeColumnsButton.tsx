import React from "react";

export default function CustomizeColumnsButton() {
  return (
    <button
      className="flex text-primary-foreground items-center gap-2 px-4 py-2 rounded-lg bg-secondary hover:bg-blue-500 hover:text-white transition-color shadow-sm text-[14px]"
      onClick={() => window.dispatchEvent(new CustomEvent("toggleColumnsModal"))}
    >
      <i className="fas fa-sliders-h"></i>
      Customize Columns
    </button>
  );
}
