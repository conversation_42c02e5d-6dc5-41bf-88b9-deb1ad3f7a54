import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Loader } from "lucide-react";

export interface ProfileFormData {
  email: string;
  firstName: string;
  lastName: string;
}

interface ProfileStepProps {
  data: ProfileFormData;
  onChange: (data: ProfileFormData) => void;
  onNext: () => void;
  errors: { [key: string]: string };
  isValid: boolean;
  isSubmitting?: boolean;
  isLoading?: boolean;
}

const ProfileStep: React.FC<ProfileStepProps> = ({
  data,
  onChange,
  onNext,
  errors,
  isValid,
  isSubmitting = false,
  isLoading = false,
}) => {
  const handleChange = (field: keyof ProfileFormData, value: string) => {
    onChange({ ...data, [field]: value });
  };

  return (
    <Card className="w-full max-w-2xl mx-auto border-t-4 border-primary shadow-md relative">
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 dark:bg-gray-800/80 flex items-center justify-center z-10 rounded-b-lg">
          <div className="flex flex-col items-center">
            <Loader className="h-8 w-8 text-primary animate-spin mb-2" />
            <p className="text-sm text-primary">Loading profile data...</p>
          </div>
        </div>
      )}
      <CardHeader className="bg-gradient-to-r from-primary/5 to-transparent">
        <CardTitle>Profile Information</CardTitle>
        <CardDescription>Tell us about yourself</CardDescription>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium mb-1">
              Email Address
            </label>
            <Input
              id="email"
              value={data.email}
              disabled
              className="bg-secondary/50"
            />
            <p className="text-sm text-gray-500 mt-1">This cannot be changed</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="firstName"
                className="block text-sm font-medium mb-1 flex"
              >
                First Name <span className="text-red-500 ml-1">*</span>
              </label>
              <div className="relative">
                <Input
                  id="firstName"
                  value={data.firstName}
                  onChange={(e) => handleChange("firstName", e.target.value)}
                  required
                  className={errors.firstName ? "border-red-500" : ""}
                  disabled={isSubmitting || isLoading}
                  placeholder={isLoading ? "Loading..." : ""}
                />
                {isLoading && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Loader className="h-4 w-4 text-primary animate-spin" />
                  </div>
                )}
              </div>
              {errors.firstName && (
                <p className="text-red-500 text-xs mt-1">{errors.firstName}</p>
              )}
            </div>

            <div>
              <label
                htmlFor="lastName"
                className="block text-sm font-medium mb-1 flex"
              >
                Last Name <span className="text-red-500 ml-1">*</span>
              </label>
              <div className="relative">
                <Input
                  id="lastName"
                  value={data.lastName}
                  onChange={(e) => handleChange("lastName", e.target.value)}
                  required
                  className={errors.lastName ? "border-red-500" : ""}
                  disabled={isSubmitting || isLoading}
                  placeholder={isLoading ? "Loading..." : ""}
                />
                {isLoading && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Loader className="h-4 w-4 text-primary animate-spin" />
                  </div>
                )}
              </div>
              {errors.lastName && (
                <p className="text-red-500 text-xs mt-1">{errors.lastName}</p>
              )}
            </div>
          </div>

          {errors.submission && (
            <div className="bg-red-50 text-red-600 p-3 rounded-lg text-sm">
              {errors.submission}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-end border-t pt-6 bg-secondary/50">
        <Button
          onClick={onNext}
          className="flex items-center gap-2 min-w-[120px]"
          disabled={!isValid || isSubmitting || isLoading}
        >
          {isSubmitting ? (
            <>
              <div className="h-4 w-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-1"></div>
              Saving...
            </>
          ) : (
            <>
              Continue <ArrowRight className="w-4 h-4" />
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ProfileStep;
