import React from "react";

interface PayoutOverview {
  total_earned: number;
  pending: number;
  paid: number;
}

interface OverviewProps {
  overview: PayoutOverview | null;
  isLoading: boolean;
}

const PayoutOverview: React.FC<OverviewProps> = ({ overview, isLoading }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  // Use data from props or fallback to defaults
  const payoutStats = {
    totalEarned: overview?.total_earned || 0,
    alreadyPaid: overview?.paid || 0,
    pending: overview?.pending || 0,
    nextPayoutDate: "Jan 26, 2025", // This could come from API in the future
  };

  return (
    <div className="space-y-6">
      {/* Payout Info Banner */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 sm:p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <div className="w-5 h-5 text-blue-500">ℹ️</div>
          </div>
          <div className="ml-3">
            <p className="text-xs sm:text-sm text-blue-800 dark:text-blue-200">
              Payouts are generated biweekly on Fridays. Minimum payout:{" "}
              <strong>$50.00</strong>
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Total Earned */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Total Earned
              </p>
              {isLoading ? (
                <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"></div>
              ) : (
                <p className="text-lg sm:text-2xl font-bold text-gray-900 dark:text-white truncate">
                  {formatCurrency(payoutStats.totalEarned)}
                </p>
              )}
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                already paid + pending
              </p>
            </div>
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center ml-3">
              <span className="text-orange-600 dark:text-orange-400">💰</span>
            </div>
          </div>
        </div>

        {/* Already Paid */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Already Paid
              </p>
              {isLoading ? (
                <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"></div>
              ) : (
                <p className="text-lg sm:text-2xl font-bold text-green-600 dark:text-green-400 truncate">
                  {formatCurrency(payoutStats.alreadyPaid)}
                </p>
              )}
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Total paid amount so far
              </p>
            </div>
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center ml-3">
              <span className="text-green-600 dark:text-green-400">✓</span>
            </div>
          </div>
        </div>

        {/* Pending */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 sm:p-6 sm:col-span-2 lg:col-span-1">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Pending
              </p>
              {isLoading ? (
                <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"></div>
              ) : (
                <p className="text-lg sm:text-2xl font-bold text-yellow-600 dark:text-yellow-400 truncate">
                  {formatCurrency(payoutStats.pending)}
                </p>
              )}
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {/* Next: {payoutStats.nextPayoutDate} */}
                Processing
              </p>
            </div>
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center ml-3">
              <span className="text-yellow-600 dark:text-yellow-400">⏳</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PayoutOverview;
