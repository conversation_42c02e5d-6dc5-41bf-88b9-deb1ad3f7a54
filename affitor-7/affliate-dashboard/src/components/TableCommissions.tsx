import React from "react";
import { Loader2 } from "lucide-react";

export interface CommissionItem {
  id: string;
  date: string;
  user: {
    username: string;
    email: string;
  };
  product: string;
  productType: "Premium" | "Free";
  grossSale: number;
  commission: number;
  status: "Paid" | "Ready To Pay" | "Under Review" | "Pending";
}

interface Pagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

interface TableCommissionsProps {
  data: CommissionItem[];
  isLoading?: boolean;
  // Pagination props
  enablePagination?: boolean;
  pagination?: Pagination | null;
  currentPage?: number;
  onPageChange?: (page: number) => void;
  isPaginationLoading?: boolean;
}

// Status badge component
const StatusBadge: React.FC<{ status: CommissionItem["status"] }> = ({
  status,
}) => {
  let bgColor = "";
  switch (status) {
    case "Paid":
      bgColor =
        "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      break;
    case "Ready To Pay":
      bgColor = "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100";
      break;
    case "Under Review":
    case "Pending":
      bgColor =
        "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100";
      break;
    default:
      bgColor = "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
  }

  return (
    <span className={`px-2 py-1 text-xs font-medium rounded ${bgColor}`}>
      {status}
    </span>
  );
};

// Product badge component
const ProductBadge: React.FC<{ type: CommissionItem["productType"] }> = ({
  type,
}) => {
  const bgColor =
    type === "Premium"
      ? "bg-gray-800 text-white dark:bg-gray-600 dark:text-white"
      : "bg-transparent text-gray-800 dark:text-gray-200 border border-gray-300 dark:border-gray-600";

  return (
    <span className={`px-2 py-1 text-xs font-medium rounded ${bgColor}`}>
      {type}
    </span>
  );
};

// Function to blur email address (same as Conversions)
const blurEmail = (email: string) => {
  if (!email) return "";
  const [localPart, domain] = email.split("@");
  if (localPart.length <= 2) {
    return `${localPart[0]}***@${domain}`;
  }
  return `${localPart.substring(0, 2)}***@${domain}`;
};

const TableCommissions: React.FC<TableCommissionsProps> = ({
  data,
  isLoading = false,
  enablePagination = false,
  pagination,
  currentPage = 1,
  onPageChange,
  isPaginationLoading = false,
}) => {
  return (
    <>
      <div className="overflow-x-auto relative">
        {/* Loading overlay similar to other tables */}
        {isLoading && (
          <div className="absolute inset-0 bg-gray-50 dark:bg-gray-700 bg-opacity-50 flex items-center justify-center z-10">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        )}

        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Customer
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Product
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Gross Sale
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Commission
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Status
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {!isLoading
              ? // Actual data
                data.map((item) => (
                  <tr
                    key={item.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                      {item.date}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {item.user?.username || "Unknown"}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {item.user?.email ? blurEmail(item.user.email) : ""}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <ProductBadge type={item.productType} />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                      ${item.grossSale.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400 font-medium">
                      ${item.commission.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <StatusBadge status={item.status} />
                    </td>
                  </tr>
                ))
              : // Loading skeleton
                Array.from({ length: 5 }).map((_, index) => (
                  <tr key={`skeleton-${index}`}>
                    {Array.from({ length: 6 }).map((_, cellIndex) => (
                      <td key={`cell-${cellIndex}`} className="px-6 py-4">
                        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 animate-pulse"></div>
                      </td>
                    ))}
                  </tr>
                ))}
            {data.length === 0 && !isLoading && (
              <tr>
                <td
                  colSpan={6}
                  className="px-6 py-8 text-center text-gray-500 dark:text-gray-400"
                >
                  No commission data available.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination Controls */}
      {enablePagination && data.length > 0 && pagination && onPageChange && (
        <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1 || isPaginationLoading}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={
                currentPage >= pagination.pageCount || isPaginationLoading
              }
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              {isPaginationLoading ? (
                <div className="flex items-center space-x-2 text-gray-500">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm">Loading page {currentPage}...</span>
                </div>
              ) : (
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Showing page{" "}
                  <span className="font-medium">{pagination.page}</span> of{" "}
                  <span className="font-medium">{pagination.pageCount}</span> (
                  {pagination.total} total items)
                </p>
              )}
            </div>
            <div>
              <nav
                className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                aria-label="Pagination"
              >
                <button
                  onClick={() => onPageChange(currentPage - 1)}
                  disabled={currentPage === 1 || isPaginationLoading}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
                >
                  <span className="sr-only">Previous</span>
                  <svg
                    className="h-5 w-5"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>

                {/* Dynamic page numbers */}
                {Array.from(
                  { length: Math.min(5, pagination.pageCount) },
                  (_, i) => {
                    let pageNum;
                    if (pagination.pageCount <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= pagination.pageCount - 2) {
                      pageNum = pagination.pageCount - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => onPageChange(pageNum)}
                        disabled={isPaginationLoading}
                        className={`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium ${
                          currentPage === pageNum
                            ? "text-blue-600 dark:text-blue-400"
                            : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600"
                        } disabled:opacity-50`}
                      >
                        {pageNum}
                      </button>
                    );
                  }
                )}

                <button
                  onClick={() => onPageChange(currentPage + 1)}
                  disabled={
                    currentPage >= pagination.pageCount || isPaginationLoading
                  }
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
                >
                  <span className="sr-only">Next</span>
                  <svg
                    className="h-5 w-5"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default TableCommissions;
