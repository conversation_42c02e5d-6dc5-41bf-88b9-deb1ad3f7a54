import { IAffiliate } from "@/interfaces";
import _ from "lodash";
import router from "next/router";
import { useState, useEffect, useMemo, useCallback } from "react";
import Loading from "../Loading";

export default function SearchNav() {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isLoadingSearch, setIsLoadingSearch] = useState(false);
  const [textSearch, setTextSearch] = useState("");
  const [searchResult, setSearchResult] = useState<Record<string, any[]>>({});
  const [searchCount, setSearchCount] = useState<Record<string, number>>({});

  const handleTextSearch = (text: string) => {
    setTextSearch(text);

    // Track search keyword if not empty
    if (text.trim()) {
      // Update local count for analytics
      setSearchCount((prev) => ({
        ...prev,
        [text]: (prev[text] || 0) + 1,
      }));
    }
  };

  const fetchAffiliatePrograms = async (textSearch: string) => {
    try {
      setIsLoadingSearch(true);
      const response = await fetch(
        `/api/affiliates?filters[name][$containsi]=${textSearch}&populate[image][fields][0]=id&populate[image][fields][1]=url&populate[industry][fields][0]=slug`
      );
      const json = await response.json();
      const { data } = json;

      if (!data) return;
      if (data.statusCode) {
        console.error("Error fetching affiliate programs:", data.message);
        return;
      }

      setSearchResult((prev) => ({
        ...prev,
        [textSearch]: data.map((program: IAffiliate) => ({
          name: program.name,
          id: program.documentId,
          image: program.image,
          industry: program.industry,
          slug: program.slug || program.documentId,
        })),
      }));
    } catch (error) {
      console.error("Error fetching affiliate programs:", error);
    } finally {
      setIsLoadingSearch(false);
    }
  };

  const programs: IAffiliate | IAffiliate[] | null = useMemo(() => {
    if (!textSearch) return null;
    if (searchResult[textSearch]) {
      return searchResult[textSearch];
    }
    fetchAffiliatePrograms(textSearch);
    return [];
  }, [textSearch, searchResult]);

  const debounceTextSearch = useCallback(
    _.debounce((text: string) => {
      handleTextSearch(text);
    }, 300),
    []
  );

  useEffect(() => {
    setIsSearchOpen(true);
    if (!textSearch) {
      setIsSearchOpen(false);
      return;
    }
  }, [textSearch]);

  return (
    <div className="relative focus-within:shadow-lg focus-within:rounded-full md:w-[400px]">
      <div className="flex items-center rounded-full overflow-hidden shadow-sm h-10">
        <span className="flex items-center justify-center w-10 h-full bg-blue-100">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-blue-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 4a6 6 0 100 12 6 6 0 000-12zm8 8l4 4"
            />
          </svg>
        </span>
        <input
          type="text"
          placeholder="Search Programs..."
          className="search-input px-4 py-2 w-full border-none focus:outline-none bg-primary"
          data-gtm-element="search-input"
          onFocus={() => {
            if (textSearch) setIsSearchOpen(true);
          }}
          onBlur={() => {
            setTimeout(() => {
              setIsSearchOpen(false);
            }, 500);
          }}
          onChange={(e) => debounceTextSearch(e.target.value)}
        />
      </div>
      <div
        className={`absolute top-full left-0 w-full bg-primary shadow-lg 
                  rounded-lg transition-all duration-300 ease-in-out mt-[5px] 
                   py-0 max-h-[250px] overflow-y-auto z-10`}
      >
        {isLoadingSearch && (
          <Loading
            className="!w-5 !h-5"
            containerClassName="!h-full p-3 text-left"
          />
        )}
        {!isLoadingSearch && programs && isSearchOpen && (
          <SearchResult programs={programs} />
        )}
      </div>
    </div>
  );

  function SearchResult({ programs }: { programs: any[] | null }) {
    console.log("Search result programs:", programs);
    if (programs && programs.length) {
      return (
        <div className="bg-primary rounded-lg shadow-md overflow-hidden divide-y divide-secondary">
          {programs.map(({ id, name, image, industry, slug }) => {
            // Debug the industry object to verify it has the expected structure
            console.log("Search result industry data:", industry);

            // Extract industry path with better error handling
            const industryPath =
              industry && typeof industry === "object" && industry.slug
                ? industry.slug
                : "vertical";
            const slugPath = slug || id;

            // Construct the URL
            const programUrl = `/${industryPath}/${slugPath}`;

            return (
              <a
                key={id}
                href={programUrl}
                onClick={(e) => {
                  e.preventDefault(); // Prevent default to handle programmatically
                  console.log(`Navigating to: ${programUrl}`);

                  // Use setTimeout to ensure the click event completes before navigation
                  // This helps avoid race conditions with the search blur handler
                  setTimeout(() => {
                    router.push(programUrl);
                  }, 10);
                }}
                className="flex items-center py-3 px-4 w-full text-left hover:bg-blue-50 transition-all duration-200 group cursor-pointer"
              >
                <div className="w-9 h-9 mr-3 flex-shrink-0 bg-primary rounded-full overflow-hidden border border-border shadow-sm">
                  <img
                    src={_.get(
                      image,
                      "url",
                      "https://example.com/default-image.jpg"
                    )}
                    alt={name}
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="overflow-hidden flex-grow">
                  <h3 className="text-sm font-medium text-primary-foreground/80 truncate group-hover:text-blue-600 transition-colors duration-200">
                    {name}
                  </h3>
                </div>
                <div className="ml-2 text group-hover:text-default transition-colors duration-200 transform group-hover:translate-x-0.5">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M9 18l6-6-6-6" />
                  </svg>
                </div>
              </a>
            );
          })}
        </div>
      );
    }
    return (
      <div className="bg-primary rounded-lg py-4 px-4 text-center text-sm text-gray-500 shadow-sm">
        <svg
          className="w-5 h-5 mx-auto mb-1 text-gray-400"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        No programs found
      </div>
    );
  }
}
