# Enhanced Link Features for YooptaRichTextEditor

## Overview

The YooptaRichTextEditor now includes Notion-like link handling that provides users with multiple options when pasting URLs into the editor.

## Features

### 1. Smart Link Detection
- Automatically detects when a URL is pasted into the editor
- Shows a modal with options for how to handle the link
- Supports various URL formats (http, https, with or without www)

### 2. Link Options Modal
When a URL is pasted, users can choose from:

#### **Link** (Always Available)
- Creates a simple clickable link
- Opens in a new tab when clicked
- Preserves the original URL text or allows custom text

#### **Embed** (For Supported Platforms)
- Embeds the content directly in the editor
- Supported platforms:
  - YouTube (youtube.com, youtu.be)
  - Vimeo (vimeo.com)
  - Twitter/X (twitter.com, x.com)
  - Instagram (instagram.com)
  - TikTok (tiktok.com)
  - CodePen (codepen.io)
  - CodeSandbox (codesandbox.io)
  - Figma (figma.com)
  - Spotify (spotify.com)
  - SoundCloud (soundcloud.com)
  - Loom (loom.com)
  - Notion (notion.so)
  - Airtable (airtable.com)

#### **Bookmark** (Always Available)
- Creates a rich preview card with title and description
- Shows the domain name and URL
- Styled as an info callout block

### 3. Video Handling
- YouTube and Vimeo URLs are automatically converted to video embeds
- Uses the Video plugin for better video playback experience
- Maintains aspect ratio and responsive design

## Usage

1. **Paste a URL**: Simply paste any URL into the editor
2. **Choose Option**: A modal will appear with available options
3. **Select**: Click on your preferred option (Link, Embed, or Bookmark)
4. **Result**: The content is inserted according to your choice

## Technical Implementation

### Components
- `LinkOptionsModal.tsx`: The modal component for selecting link options
- `link-utils.ts`: Utility functions for URL processing and block creation

### Key Functions
- `isValidUrl()`: Validates if text is a proper URL
- `isEmbeddableUrl()`: Checks if URL can be embedded
- `createLinkBlock()`: Creates a simple link block
- `createEmbedBlock()`: Creates an embed block
- `createVideoBlock()`: Creates a video block
- `createBookmarkBlock()`: Creates a bookmark-style block

### Styling
- Enhanced CSS in `yoopta-notion.css` for embed and video blocks
- Dark theme support for all new components
- Responsive design for different screen sizes

## Benefits

1. **User Experience**: Notion-like interface that users are familiar with
2. **Flexibility**: Multiple ways to handle the same URL
3. **Rich Content**: Embeds provide richer content experience
4. **Accessibility**: All options maintain proper accessibility standards
5. **Performance**: Lazy loading for embedded content

## Browser Support

- Modern browsers with clipboard API support
- Fallback handling for older browsers
- Cross-platform compatibility (desktop and mobile)

## Future Enhancements

- Server-side metadata fetching for better bookmark previews
- Additional embed providers
- Custom embed configurations
- Bulk link processing
- Link preview on hover
