import React, { useState } from 'react';
import { Button } from '@/components/ui/button';

interface LinkPasteDebuggerProps {
  onTestPaste: (url: string) => void;
}

const LinkPasteDebugger: React.FC<LinkPasteDebuggerProps> = ({ onTestPaste }) => {
  const [testUrl, setTestUrl] = useState('https://www.youtube.com/watch?v=dQw4w9WgXcQ');
  const [isVisible, setIsVisible] = useState(false);

  const testUrls = [
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    'https://google.com',
    'https://github.com/yoopta-editor/Yoopta-Editor',
    'https://www.figma.com/file/example',
    'https://twitter.com/example/status/123',
    'https://vimeo.com/123456789'
  ];

  const handleTestPaste = (url: string) => {
    console.log('[LinkPasteDebugger] Testing paste with URL:', url);
    
    // Create a synthetic paste event
    const clipboardData = new DataTransfer();
    clipboardData.setData('text/plain', url);
    
    const pasteEvent = new ClipboardEvent('paste', {
      clipboardData: clipboardData,
      bubbles: true,
      cancelable: true
    });
    
    // Find the active contenteditable element
    const activeElement = document.activeElement;
    const contentEditable = activeElement?.closest('[contenteditable="true"]') || 
                           document.querySelector('[contenteditable="true"]');
    
    if (contentEditable) {
      contentEditable.dispatchEvent(pasteEvent);
      console.log('[LinkPasteDebugger] Paste event dispatched to:', contentEditable);
    } else {
      console.warn('[LinkPasteDebugger] No contenteditable element found');
    }
    
    onTestPaste(url);
  };

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        variant="outline"
        size="sm"
        className="fixed bottom-4 right-4 z-50 bg-blue-500 text-white hover:bg-blue-600"
      >
        🔗 Test Link Paste
      </Button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 w-80">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-semibold text-gray-900 dark:text-gray-100">Link Paste Debugger</h3>
        <Button
          onClick={() => setIsVisible(false)}
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
        >
          ×
        </Button>
      </div>
      
      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Test URL:
          </label>
          <input
            type="text"
            value={testUrl}
            onChange={(e) => setTestUrl(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            placeholder="Enter URL to test..."
          />
        </div>
        
        <Button
          onClick={() => handleTestPaste(testUrl)}
          className="w-full"
          size="sm"
        >
          Simulate Paste
        </Button>
        
        <div className="border-t border-gray-200 dark:border-gray-600 pt-3">
          <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">Quick test URLs:</p>
          <div className="space-y-1">
            {testUrls.map((url, index) => (
              <Button
                key={index}
                onClick={() => handleTestPaste(url)}
                variant="ghost"
                size="sm"
                className="w-full justify-start text-xs h-8 px-2"
              >
                {url.length > 35 ? url.substring(0, 35) + '...' : url}
              </Button>
            ))}
          </div>
        </div>
        
        <div className="border-t border-gray-200 dark:border-gray-600 pt-3">
          <p className="text-xs text-gray-600 dark:text-gray-400">
            💡 Open browser console to see debug logs
          </p>
        </div>
      </div>
    </div>
  );
};

export default LinkPasteDebugger;
