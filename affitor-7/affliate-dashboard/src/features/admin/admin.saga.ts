import { call, put, takeEvery } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import { actions } from "./admin.slice";

function* handleLogin(
  action: PayloadAction<{ email: string; password: string }>
): Generator<any, void, any> {
  try {
    const { email, password } = action.payload;

    // Use the proxy API endpoint instead of direct StrapiClient call
    const response: any = yield call(fetch, "/api/admin/login", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      const errorData = yield response.json();
      throw new Error(errorData.error || "Login failed");
    }

    const responseData = yield response.json();

    // Handle the correct response structure: data.user and data.token
    if (
      responseData &&
      responseData.data &&
      responseData.data.user &&
      responseData.data.token
    ) {
      // Store admin token and data in localStorage
      if (typeof window !== "undefined") {
        localStorage.setItem("admin_token", responseData.data.token);
        localStorage.setItem(
          "admin_data",
          JSON.stringify(responseData.data.user)
        );
      }

      yield put(
        actions.loginSuccess({
          admin: responseData.data.user, // Use user data as admin data
          token: responseData.data.token,
        })
      );

      // Use Next.js router dispatch to navigate to dashboard immediately
      // This works with any Next.js component that subscribes to router events
    } else {
      yield put(actions.loginFailure("Invalid response from server"));
    }
  } catch (error: any) {
    console.error("Admin login error:", error);
    const errorMessage = error?.message || "Login failed";
    yield put(actions.loginFailure(errorMessage));
  }
}

function* handleLogout(): Generator<any, void, any> {
  try {
    console.log(
      "Admin logout: removing admin_token and admin_data from localStorage"
    );
    // Remove admin token and data from localStorage
    if (typeof window !== "undefined") {
      localStorage.removeItem("admin_token");
      localStorage.removeItem("admin_data");
    }

    // Dispatch logout success to complete the logout process
    // yield put(actions.logout());
  } catch (error: any) {
    console.error("Admin logout error:", error);
  }
}

function* handleCheckAuthStatus(): Generator<any, void, any> {
  try {
    // Get admin token from localStorage
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem("admin_token")
        : null;

    if (!token) {
      yield put(actions.checkAuthStatusFailure("No admin token found"));
      return;
    }

    // Validate token by making a request to a protected endpoint
    const response: any = yield call(fetch, "/api/admin/dashboard", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      // Token is invalid or expired
      if (typeof window !== "undefined") {
        localStorage.removeItem("admin_token");
      }

      if (response.status === 401) {
        yield put(actions.checkAuthStatusFailure("Admin session expired"));
      } else {
        yield put(actions.checkAuthStatusFailure("Token validation failed"));
      }
      return;
    }

    // Token is valid, get admin data (we can extract from token or make another request)
    // For now, we'll assume token is valid and use stored admin data
    const storedAdminData =
      typeof window !== "undefined"
        ? JSON.parse(localStorage.getItem("admin_data") || "null")
        : null;

    if (storedAdminData) {
      yield put(
        actions.checkAuthStatusSuccess({
          admin: storedAdminData,
          token: token,
        })
      );
    } else {
      // If no stored admin data, mark as failed
      yield put(actions.checkAuthStatusFailure("Admin data not found"));
    }
  } catch (error: any) {
    console.error("Admin auth status check error:", error);

    // Clear invalid token
    if (typeof window !== "undefined") {
      localStorage.removeItem("admin_token");
    }

    yield put(actions.checkAuthStatusFailure("Authentication check failed"));
  }
}

function* handleFetchPartners(
  action: PayloadAction<{
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
    sort?: string;
    username?: string;
    email?: string;
    createdAtFrom?: string;
    createdAtTo?: string;
    revenueFrom?: string;
    revenueTo?: string;
    commissionFrom?: string;
    commissionTo?: string;
    clicksFrom?: string;
    clicksTo?: string;
    leadsFrom?: string;
    leadsTo?: string;
    conversionsFrom?: string;
    conversionsTo?: string;
  }>
): Generator<any, void, any> {
  try {
    const {
      page = 1,
      pageSize = 10,
      search = "",
      status,
      sort,
      username,
      email,
      createdAtFrom,
      createdAtTo,
      revenueFrom,
      revenueTo,
      commissionFrom,
      commissionTo,
      clicksFrom,
      clicksTo,
      leadsFrom,
      leadsTo,
      conversionsFrom,
      conversionsTo,
    } = action.payload;

    // Get admin token from localStorage
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem("admin_token")
        : null;

    if (!token) {
      yield put(actions.fetchPartnersFailure("Admin authentication required"));
      return;
    }

    // Use the proxy API endpoint
    const queryParams = new URLSearchParams({
      page: page.toString(),
      pageSize: pageSize.toString(),
      ...(search && { search }),
      ...(status && { status }),
      ...(sort && { sort }),
      ...(username && { username }),
      ...(email && { email }),
      ...(createdAtFrom && { createdAtFrom }),
      ...(createdAtTo && { createdAtTo }),
      ...(revenueFrom && { revenueFrom }),
      ...(revenueTo && { revenueTo }),
      ...(commissionFrom && { commissionFrom }),
      ...(commissionTo && { commissionTo }),
      ...(clicksFrom && { clicksFrom }),
      ...(clicksTo && { clicksTo }),
      ...(leadsFrom && { leadsFrom }),
      ...(leadsTo && { leadsTo }),
      ...(conversionsFrom && { conversionsFrom }),
      ...(conversionsTo && { conversionsTo }),
    });

    const response: any = yield call(
      fetch,
      `/api/admin/partners?${queryParams}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      const errorData = yield response.json();
      throw new Error(errorData.error || "Failed to fetch partners");
    }

    const data = yield response.json();

    yield put(actions.fetchPartnersSuccess(data));
  } catch (error: any) {
    console.error("Fetch partners error:", error);
    const errorMessage = error?.message || "Failed to fetch partners";
    yield put(actions.fetchPartnersFailure(errorMessage));
  }
}

function* handleFetchDashboardStats(): Generator<any, void, any> {
  try {
    // Get admin token from localStorage
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem("admin_token")
        : null;

    if (!token) {
      yield put(
        actions.fetchDashboardStatsFailure("Admin authentication required")
      );
      return;
    }

    // Use the proxy API endpoint
    const response: any = yield call(fetch, `/api/admin/dashboard`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = yield response.json();
      throw new Error(errorData.error || "Failed to fetch dashboard stats");
    }

    const responseData = yield response.json();

    if (responseData && responseData.data) {
      yield put(actions.fetchDashboardStatsSuccess(responseData.data));
    } else {
      yield put(actions.fetchDashboardStatsFailure("Invalid response format"));
    }
  } catch (error: any) {
    console.error("Fetch dashboard stats error:", error);
    const errorMessage = error?.message || "Failed to fetch dashboard stats";
    yield put(actions.fetchDashboardStatsFailure(errorMessage));
  }
}

function* handleFetchSettings(): Generator<any, void, any> {
  try {
    // Get admin token from localStorage
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem("admin_token")
        : null;

    if (!token) {
      yield put(actions.fetchSettingsFailure("Admin authentication required"));
      return;
    }

    // Use the proxy API endpoint
    const response: any = yield call(fetch, `/api/admin/settings`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = yield response.json();
      throw new Error(errorData.error || "Failed to fetch settings");
    }

    const responseData = yield response.json();
    yield put(actions.fetchSettingsSuccess(responseData));
  } catch (error: any) {
    console.error("Fetch settings error:", error);
    const errorMessage = error?.message || "Failed to fetch settings";
    yield put(actions.fetchSettingsFailure(errorMessage));
  }
}

function* handleUpdateSettings(
  action: PayloadAction<any>
): Generator<any, void, any> {
  try {
    // Get admin token from localStorage
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem("admin_token")
        : null;

    if (!token) {
      yield put(actions.updateSettingsFailure("Admin authentication required"));
      return;
    }

    // Use the proxy API endpoint
    const response: any = yield call(fetch, `/api/admin/settings`, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(action.payload),
    });

    if (!response.ok) {
      const errorData = yield response.json();
      throw new Error(errorData.error || "Failed to update settings");
    }

    const responseData = yield response.json();
    yield put(actions.updateSettingsSuccess(responseData));
  } catch (error: any) {
    console.error("Update settings error:", error);
    const errorMessage = error?.message || "Failed to update settings";
    yield put(actions.updateSettingsFailure(errorMessage));
  }
}

export default function* adminSaga() {
  yield takeEvery(actions.login.type, handleLogin);
  yield takeEvery(actions.logout.type, handleLogout);
  yield takeEvery(actions.checkAuthStatus.type, handleCheckAuthStatus);
  yield takeEvery(actions.fetchPartners.type, handleFetchPartners);
  yield takeEvery(actions.fetchDashboardStats.type, handleFetchDashboardStats);
  yield takeEvery(actions.fetchSettings.type, handleFetchSettings);
  yield takeEvery(actions.updateSettings.type, handleUpdateSettings);
}
