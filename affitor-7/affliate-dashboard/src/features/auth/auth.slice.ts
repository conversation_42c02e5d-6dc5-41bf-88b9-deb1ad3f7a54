import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { createSelector } from "reselect";
import { RootState } from "@/store";

export interface User {
  id: string;
  username: string;
  email: string;
  // Add other user properties as needed
}

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
  requireAuth: string | null;
  requireUpgrade: string | null; // New field for upgrade required messages
  googleAuthUrl: string | null;
  successMessage: string | null;
  socialLoginLoading: boolean;
}

// Safe localStorage helper functions for SSR
const isBrowser = typeof window !== "undefined";

// Storage utility functions
const getStoredAuthToken = () => {
  if (!isBrowser) return null;
  return localStorage.getItem("auth_token");
};

const getStoredUser = () => {
  if (!isBrowser) return null;
  const user = localStorage.getItem("user");
  return user ? JSON.parse(user) : null;
};

const storeAuthToken = (token: string) => {
  if (isBrowser) {
    localStorage.setItem("auth_token", token);
  }
};

const storeUser = (user: any) => {
  if (isBrowser) {
    localStorage.setItem("user", JSON.stringify(user));
  }
};

const clearAuthStorage = () => {
  if (isBrowser) {
    localStorage.removeItem("auth_token");
    localStorage.removeItem("user");
  }
};

// Initial state based on localStorage when in browser, default values for SSR
const initialState: AuthState = {
  isAuthenticated: isBrowser ? !!getStoredAuthToken() : false,
  user: isBrowser ? getStoredUser() : null,
  loading: false,
  error: null,
  requireAuth: null,
  requireUpgrade: null, // Initialize new field
  googleAuthUrl: null,
  successMessage: null,
  socialLoginLoading: false,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    // Firebase Authentication
    firebaseGoogleSignIn: (state) => {
      state.error = null;
      state.socialLoginLoading = true;
      state.loading = false; // Ensure general loading is false when social loading starts
    },

    // Actions to get Google Auth URL
    getGoogleAuthUrl: (state) => {
      state.loading = true;
      state.error = null;
    },
    setGoogleAuthUrl: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.googleAuthUrl = action.payload;
    },

    // Handle token received from backend callback
    handleAuthToken: (
      state,
      action: PayloadAction<{ token?: string; user?: User; error?: string }>
    ) => {
      state.loading = true;
      state.error = null;
    },

    // Actions for Google authentication (success case after backend handled callback)
    setAuthenticatedUser: (
      state,
      action: PayloadAction<{ user: User; jwt: string }>
    ) => {
      state.loading = false;
      state.socialLoginLoading = false;
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.error = null; // Clear any previous errors

      // Store authentication data in localStorage
      storeAuthToken(action.payload.jwt);
      storeUser(action.payload.user);
    },

    // Utility actions
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
      if (!action.payload) {
        state.socialLoginLoading = false; // Reset social loading when general loading is cleared
      }
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.loading = false;
      state.socialLoginLoading = false;
      state.error = action.payload;
    },
    setRequireAuth: (state, action: PayloadAction<string | null>) => {
      state.requireAuth = action.payload;
    },
    // New action for upgrade required messages
    setRequireUpgrade: (state, action: PayloadAction<string | null>) => {
      state.requireUpgrade = action.payload;
    },
    setAuthSuccess: (state, action: PayloadAction<string | null>) => {
      state.successMessage = action.payload;
      state.error = null;
    },
    // Update clearMessages to also clear requireUpgrade
    clearMessages: (state) => {
      state.error = null;
      state.successMessage = null;
      state.requireAuth = null;
      state.requireUpgrade = null;
      state.socialLoginLoading = false; // Also reset social loading when clearing messages
    },

    // Check auth status
    checkAuthStatus: (state) => {
      // Update state directly from localStorage
      state.isAuthenticated = !!getStoredAuthToken();
      state.user = getStoredUser();
    },
    setAuthStatus: (
      state,
      action: PayloadAction<{ isAuthenticated: boolean; user: User | null }>
    ) => {
      state.isAuthenticated = action.payload.isAuthenticated;
      state.user = action.payload.user;
    },

    // Logout
    logout: (state) => {
      // Clear localStorage on logout
      console.log("logout");
      clearAuthStorage();
      state.isAuthenticated = false;
      state.user = null;
    },
    clearAuthData: (state) => {
      state.isAuthenticated = false;
      state.user = null;
    },
  },
});

// Export the localStorage utility functions for use in sagas or components if needed
export const authStorage = {
  getStoredAuthToken,
  getStoredUser,
  storeAuthToken,
  storeUser,
  clearAuthStorage,
};

export const { actions, reducer } = authSlice;

// Selectors
const selectAuthState = (state: RootState) => state.auth;

export const selectIsAuthenticated = createSelector(
  [selectAuthState],
  (authState) => authState.isAuthenticated
);

export const selectUser = createSelector(
  [selectAuthState],
  (authState) => authState.user
);

export const selectAuthLoading = createSelector(
  [selectAuthState],
  (authState) => authState.loading
);

export const selectAuthError = createSelector(
  [selectAuthState],
  (authState) => authState.error
);

export const selectGoogleAuthUrl = createSelector(
  [selectAuthState],
  (authState) => authState.googleAuthUrl
);

// Add selector for the new state
export const selectRequireAuth = createSelector(
  [selectAuthState],
  (authState) => authState.requireAuth
);

// Add selector for the new state
export const selectRequireUpgrade = createSelector(
  [selectAuthState],
  (authState) => authState.requireUpgrade
);
