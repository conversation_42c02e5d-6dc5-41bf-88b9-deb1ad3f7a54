import { ICategory } from "@/interfaces";
import { RootState } from "@/store";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { createSelector } from "reselect";

interface CategoryState {
  list: ICategory[];
  current?: ICategory;
  loading: boolean;
  error: string | null;
}

const initialState: CategoryState = {
  list: [
    {
      id: "",
      documentId: "",
      name: "All Programs",
      slug: ""
    },
  ],
  current: {
    id: "",
    documentId: "",
    name: "All Programs",
    slug: ""
  },
  loading: false,
  error: null
};

const categorySlice = createSlice({
  name: "category",
  initialState,
  reducers: {
    // Sync actions
    setCategories: (state, action: PayloadAction<ICategory[]>) => {
      const newCategories = action.payload
        ? action.payload?.filter(
            (category) =>
              !state.list.some((existing) => existing.id === category.id)
          )
        : [];
      state.list = [...state.list, ...newCategories];
    },
    setCurrentCategory: (state, action: PayloadAction<ICategory>) => {
      state.current = action.payload;
    },
    setLoadingCategory: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    // Trigger actions for Saga
    fetchAll: () => {},
    setCategoryById: (state, action: PayloadAction<string>) => {
      // This is just an action creator for the saga, no state changes here
    },
    setCategoryBySlug: (state, action: PayloadAction<string>) => {
      // This is just an action creator for the saga, no state changes here
    },
  },
});

export const { actions, reducer } = categorySlice;
const selectCategoryState = (state: RootState) => state.category;

// category
export const selectCategories = createSelector(
  [selectCategoryState],
  (categoryState) => categoryState.list
);
export const selectFilterCategory = createSelector(
  [selectCategoryState],
  (categoryState) => categoryState.current
);
export const selectCategoryLoading = createSelector(
  [selectCategoryState],
  (categoryState) => categoryState.loading
);
export const selectCategoryError = createSelector(
  [selectCategoryState],
  (categoryState) => categoryState.error
);

