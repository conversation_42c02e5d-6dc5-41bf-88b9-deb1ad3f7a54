import { call, put, takeLatest } from "redux-saga/effects";
import { actions } from "./payment-method.slice";
import { handleApiError } from "@/utils/error-handler";

function* handleFetchPaymentMethods(): Generator<any, void, any> {
  try {
    yield put(actions.setLoading(true));

    const response: Response = yield call(fetch, "/api/payment-methods");
    
    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        return; // Error was handled
      }
      
      const errorMsg = `Failed to fetch payment methods: ${response.status} ${response.statusText}`;
      console.error(errorMsg);
      yield put(actions.setError(errorMsg));
      return;
    }
    
    const { data, meta } = yield response.json();
    
    yield put(actions.setPaymentMethods(data));
    if (meta?.pagination) {
      yield put(actions.setPagination(meta.pagination));
    }
  } catch (error: any) {
    yield put(actions.setError("Failed to fetch payment methods"));
  } finally {
    yield put(actions.setLoading(false));
  }
}

export default function* paymentMethodSaga() {
  yield takeLatest(actions.fetchPaymentMethods.type, handleFetchPaymentMethods);
}
