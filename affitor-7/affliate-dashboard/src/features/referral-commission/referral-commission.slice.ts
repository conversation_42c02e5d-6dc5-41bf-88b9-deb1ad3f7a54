import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface ReferralCommission {
  id: number;
  documentId: string;
  commission_amount: number;
  commission_status: string | null;
  review_due_date: string | null;
  payment_date: string | null;
  gross_sale_amount: number;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  referrer: any;
  referral: any;
  subscription_tier: any;
}

export interface ReferralCommissionPagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

export interface ReferralCommissionStats {
  totalCommissions: number;
  pendingCommissions: number;
  paidCommissions: number;
  readyCommissions: number;
  totalEarnings: number;
  pendingEarnings: number;
  paidEarnings: number;
  readyEarnings: number;
}

interface ReferralCommissionState {
  commissions: ReferralCommission[];
  stats: ReferralCommissionStats | null;
  loading: boolean;
  statsLoading: boolean;
  error: string | null;
  pagination: ReferralCommissionPagination | null;
}

const initialState: ReferralCommissionState = {
  commissions: [],
  stats: null,
  loading: false,
  statsLoading: false,
  error: null,
  pagination: null,
};

const referralCommissionSlice = createSlice({
  name: "referralCommission",
  initialState,
  reducers: {
    // Fetch commissions actions
    fetchCommissionsRequest: (
      state,
      action: PayloadAction<{
        page?: number;
        pageSize?: number;
        isAdmin?: boolean;
      }>
    ) => {
      state.loading = true;
      state.error = null;
    },
    fetchCommissionsSuccess: (
      state,
      action: PayloadAction<{
        data: ReferralCommission[];
        meta: { pagination: ReferralCommissionPagination };
      }>
    ) => {
      state.loading = false;
      state.commissions = action.payload.data;
      state.pagination = action.payload.meta.pagination;
      state.error = null;
    },
    fetchCommissionsFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Fetch stats actions
    fetchStatsRequest: (state) => {
      state.statsLoading = true;
      state.error = null;
    },
    fetchStatsSuccess: (
      state,
      action: PayloadAction<ReferralCommissionStats>
    ) => {
      state.statsLoading = false;
      state.stats = action.payload;
      state.error = null;
    },
    fetchStatsFailure: (state, action: PayloadAction<string>) => {
      state.statsLoading = false;
      state.error = action.payload;
    },

    // Clear actions
    clearCommissions: (state) => {
      state.commissions = [];
      state.stats = null;
      state.pagination = null;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const actions = referralCommissionSlice.actions;
export const reducer = referralCommissionSlice.reducer;

// Selectors
export const selectReferralCommissions = (state: any) =>
  state.referralCommission.commissions;
export const selectReferralCommissionsLoading = (state: any) =>
  state.referralCommission.loading;
export const selectReferralCommissionsError = (state: any) =>
  state.referralCommission.error;
export const selectReferralCommissionsPagination = (state: any) =>
  state.referralCommission.pagination;

// Add new selectors
export const selectReferralCommissionStats = (state: any) =>
  state.referralCommission.stats;
export const selectReferralCommissionStatsLoading = (state: any) =>
  state.referralCommission.statsLoading;
