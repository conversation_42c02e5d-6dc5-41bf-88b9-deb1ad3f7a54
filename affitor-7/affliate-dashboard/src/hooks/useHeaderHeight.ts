"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

export const useHeaderHeight = () => {
  const [headerHeight, setHeaderHeight] = useState(75); // Default header height
  const router = useRouter();

  // Pages that should hide the header
  const hideHeaderPages = ['/login', '/register', '/forgot-password', '/reset-password'];
  const shouldHideHeader = hideHeaderPages.includes(router.pathname);

  useEffect(() => {
    if (shouldHideHeader) {
      setHeaderHeight(0);
      return;
    }

    // Measure actual header height
    const measureHeaderHeight = () => {
      const header = document.querySelector('header');
      if (header) {
        const height = header.getBoundingClientRect().height;
        setHeaderHeight(height);
      }
    };

    // Measure on mount and when window resizes
    measureHeaderHeight();
    window.addEventListener('resize', measureHeaderHeight);

    // Use ResizeObserver if available for more accurate measurements
    if (typeof ResizeObserver !== 'undefined') {
      const header = document.querySelector('header');
      if (header) {
        const resizeObserver = new ResizeObserver(() => {
          measureHeaderHeight();
        });
        resizeObserver.observe(header);

        return () => {
          resizeObserver.disconnect();
          window.removeEventListener('resize', measureHeaderHeight);
        };
      }
    }

    return () => {
      window.removeEventListener('resize', measureHeaderHeight);
    };
  }, [shouldHideHeader, router.pathname]);

  return {
    headerHeight,
    shouldHideHeader
  };
};
