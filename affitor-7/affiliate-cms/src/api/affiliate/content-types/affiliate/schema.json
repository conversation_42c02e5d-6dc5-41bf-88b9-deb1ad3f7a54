{"kind": "collectionType", "collectionName": "affiliates", "info": {"singularName": "affiliate", "pluralName": "affiliates", "displayName": "Affiliate", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "categories": {"type": "relation", "relation": "manyToMany", "target": "api::category.category", "inversedBy": "affiliates"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "url": {"type": "string"}, "monthly_traffic": {"type": "biginteger"}, "tags": {"type": "relation", "relation": "manyToMany", "target": "api::tag.tag", "mappedBy": "affiliates"}, "detail": {"type": "blocks"}, "company_name": {"type": "string"}, "tag_line": {"type": "string"}, "traffic_rank": {"type": "integer"}, "cookies_duration": {"type": "string"}, "payment_methods": {"type": "relation", "relation": "manyToMany", "target": "api::payment-method.payment-method", "inversedBy": "affiliates"}, "minimum_payout": {"type": "decimal"}, "launch_year": {"type": "integer"}, "features": {"type": "text", "default": "feature1, feature2,"}, "country": {"type": "enumeration", "enum": ["Afghanistan (AF)", "Albania (AL)", "Algeria (DZ)", "Andorra (AD)", "Angola (AO)", "Antigua and Barbuda (AG)", "Argentina (AR)", "Armenia (AM)", "Australia (AU)", "Austria (AT)", "Azerbaijan (AZ)", "Bahamas (BS)", "Bahrain (BH)", "Bangladesh (BD)", "Barbados (BB)", "Belarus (BY)", "Belgium (BE)", "Belize (BZ)", "Benin (BJ)", "Bhutan (BT)", "Bolivia (BO)", "Bosnia and Herzegovina (BA)", "Botswana (BW)", "Brazil (BR)", "Brunei (BN)", "Bulgaria (BG)", "Burkina Faso (BF)", "Burundi (BI)", "Cambodia (KH)", "Cameroon (CM)", "Canada (CA)", "Central African Republic (CF)", "<PERSON> (TD)", "Chile (CL)", "China (CN)", "Colombia (CO)", "Comoros (KM)", "Congo (CG)", "Congo (DRC) (CD)", "Costa Rica (CR)", "Côte d'Ivoire (CI)", "Croatia (HR)", "Cuba (CU)", "Cyprus (CY)", "Czech Republic (CZ)", "Denmark (DK)", "<PERSON><PERSON><PERSON><PERSON> (DJ)", "Dominica (DM)", "Dominican Republic (DO)", "Ecuador (EC)", "Egypt (EG)", "El Salvador (SV)", "Equatorial Guinea (GQ)", "<PERSON><PERSON><PERSON> (ER)", "Estonia (EE)", "Ethiopia (ET)", "Fiji (FJ)", "Finland (FI)", "France (FR)", "Gabon (GA)", "Gambia (GM)", "Georgia (GE)", "Germany (DE)", "Ghana (GH)", "Greece (GR)", "Grenada (GD)", "Guatemala (GT)", "Guinea (GN)", "Guinea-Bissau (GW)", "Guyana (GY)", "Haiti (HT)", "Honduras (HN)", "Hungary (HU)", "Iceland (IS)", "India (IN)", "Indonesia (ID)", "Iran (IR)", "Iraq (IQ)", "Ireland (IE)", "Israel (IL)", "Italy (IT)", "Jamaica (JM)", "Japan (JP)", "<PERSON> (JO)", "Kazakhstan (KZ)", "Kenya (KE)", "Ki<PERSON>bati (KI)", "North Korea (KP)", "South Korea (KR)", "Kosovo (XK)", "Kuwait (KW)", "Kyrgyzstan (KG)", "Laos (LA)", "Latvia (LV)", "Lebanon (LB)", "Lesotho (LS)", "Liberia (LR)", "Libya (LY)", "Lithuania (LT)", "Luxembourg (LU)", "Macedonia (MK)", "Madagascar (MG)", "Malawi (MW)", "Malaysia (MY)", "Maldives (MV)", "Mali (ML)", "Malta (MT)", "Marshall Islands (MH)", "Mauritania (MR)", "Mauritius (MU)", "Mexico (MX)", "Micronesia (FM)", "Moldova (MD)", "<PERSON> (MC)", "Mongolia (MN)", "Montenegro (ME)", "Morocco (MA)", "Mozambique (MZ)", "Myanmar (MM)", "Namibia (NA)", "Nauru (NR)", "Nepal (NP)", "Netherlands (NL)", "New Zealand (NZ)", "Nicaragua (NI)", "Niger (NE)", "Nigeria (NG)", "Norway (NO)", "Oman (OM)", "Pakistan (PK)", "Palau (PW)", "Panama (PA)", "Papua New Guinea (PG)", "Paraguay (PY)", "Peru (PE)", "Philippines (PH)", "Poland (PL)", "Portugal (PT)", "Qatar (QA)", "Romania (RO)", "Russia (RU)", "Rwanda (RW)", "Saint Kitts and Nevis (KN)", "Saint Lucia (LC)", "<PERSON> and the Grenadines (VC)", "Samoa (WS)", "<PERSON> (SM)", "Sao Tome and Principe (ST)", "Saudi Arabia (SA)", "Senegal (SN)", "Serbia (RS)", "Seychelles (SC)", "Sierra Leone (SL)", "Singapore (SG)", "Sint Maarten (SX)", "Slovakia (SK)", "Slovenia (SI)", "Solomon Islands (SB)", "Somalia (SO)", "South Africa (ZA)", "South Sudan (SS)", "Spain (ES)", "Sri Lanka (LK)", "Sudan (SD)", "Suriname (SR)", "Sweden (SE)", "Switzerland (CH)", "Syria (SY)", "Tajikistan (TJ)", "Tanzania (TZ)", "Thailand (TH)", "Timor-Leste (TL)", "Togo (TG)", "Tonga (TO)", "Trinidad and Tobago (TT)", "Tunisia (TN)", "Turkey (TR)", "Turkmenistan (TM)", "<PERSON><PERSON><PERSON> (TV)", "Uganda (UG)", "Ukraine (UA)", "United Arab Emirates (AE)", "United Kingdom (GB)", "United States (US)", "Uruguay (UY)", "Uzbekistan (UZ)", "Vanuatu (VU)", "Vatican City (VA)", "Venezuela (VE)", "Viet Nam (VN)", "Yemen (YE)", "Zambia (ZM)", "Zimbabwe (ZW)"]}, "contact_information": {"type": "string"}, "commission_detail": {"type": "blocks"}, "commission": {"type": "relation", "relation": "manyToOne", "target": "api::commission.commission", "inversedBy": "affiliates"}, "brand_keywords_youtube": {"type": "text"}, "currency": {"type": "enumeration", "enum": ["USD", "EUR"], "default": "USD"}, "traffic_webs": {"type": "relation", "relation": "oneToMany", "target": "api::traffic-web.traffic-web", "mappedBy": "affiliate"}, "brand_keywords_tiktok": {"type": "text"}, "domain": {"type": "string"}, "social_logs": {"type": "relation", "relation": "oneToMany", "target": "api::social-log.social-log", "mappedBy": "affiliate"}, "avg_conversion": {"type": "decimal"}, "slug": {"type": "string", "unique": true}, "pricing_range": {"type": "component", "repeatable": false, "component": "shared.pricing-range"}, "avg_price": {"type": "decimal"}, "pricing": {"type": "string"}, "recurring": {"type": "enumeration", "enum": ["One time", "Life time", "In 1 month", "In 2 months", "In 3 months", "In 4 months", "In 5 months", "In 6 months", "In 9 months", "In 12 months", "In 15 months", "In 18 months", "In 24 months", "In 36 months"]}, "recurring_priority": {"type": "integer"}, "industry": {"type": "relation", "relation": "manyToOne", "target": "api::industry.industry", "inversedBy": "affiliates"}, "airtable_data": {"type": "component", "repeatable": false, "component": "shared.airtable-config"}, "from_airtable": {"type": "boolean", "default": false}, "user_submitted": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "youtube_ad_keyword": {"type": "string"}, "tiktok_ad_keyword": {"type": "string"}}}