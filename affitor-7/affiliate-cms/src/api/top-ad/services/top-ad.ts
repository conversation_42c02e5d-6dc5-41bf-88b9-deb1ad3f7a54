/* eslint-disable no-case-declarations */
/**
 * top-ad service
 */

import { factories } from '@strapi/strapi';
import {
  TiktokCreativeCenterClient,
  YoutubeTranscriptClient,
  TiktokTranscriptClient,
} from '../../../utils/request';
import { AdplexityClient } from '../../../utils/puppeteer';
import {
  IAd,
  ITikTokAd,
  ITikTokAdResponse,
  ITikTokAdDetailResponse,
  IYoutubeAd,
  IYoutubeAdResponse,
} from '../interfaces';
import crypto from 'crypto';

export default factories.createCoreService('api::top-ad.top-ad', ({ strapi }) => ({
  /**
   * Helper function to split a keyword string into an array of keywords
   * @param {string} keywordString - String of keywords separated by "|"
   * @returns {string[]} Array of trimmed keywords
   */
  splitKeywords(keywordString?: string): string[] {
    if (!keywordString) return [];
    return keywordString
      .split('|')
      .map((k) => k.trim())
      .filter((k) => k.length > 0);
  },

  /**
   * Get keywords from the affiliate's most recent traffic web record
   * @param {any} affiliate - The affiliate object
   * @param {string} platform - The platform to get keywords for ('youtube' or 'tiktok')
   * @returns {string[]} Array of relevant keywords
   */
  async getAffiliateKeywordsFromTrafficWeb(
    affiliate: any,
    platform: string = 'all'
  ): Promise<string[]> {
    try {
      if (!affiliate?.id) {
        console.log('No affiliate ID provided for traffic web keyword lookup');
        return [];
      }

      // Get the most recent traffic web record for this affiliate
      const trafficWebs = await strapi.entityService.findMany('api::traffic-web.traffic-web', {
        filters: {
          affiliate: affiliate.id,
        },
        sort: { year: 'desc', month: 'desc' },
        limit: 1,
      });

      if (!trafficWebs || !trafficWebs.length || !trafficWebs[0].top_keyword) {
        console.log(`No traffic web data found for affiliate ${affiliate.id}`);
        return [];
      }

      const trafficWeb: any = trafficWebs[0];
      console.log(`Found traffic web data for affiliate ${affiliate.id}: ${trafficWeb.id}`);

      // Extract keywords from the traffic web data
      const topKeywords = trafficWeb.top_keyword?.keywords || [];
      if (!topKeywords.length) {
        console.log(`No keywords found in traffic web data for affiliate ${affiliate.id}`);
        return [];
      }

      console.log(`Found ${topKeywords.length} keywords in traffic web data`);

      // Filter keywords that include the affiliate name or domain
      const affiliateName = affiliate.name?.toLowerCase() || '';
      const domain = affiliate.domain?.toLowerCase() || '';

      // Extract the domain name without TLD for matching
      const domainBase = domain.split('.')[0] || '';

      // Platform-specific keyword selection strategy
      let relevantKeywords = [];

      if (platform === 'youtube' || platform === 'all') {
        // For YouTube, prioritize longer, more descriptive keywords
        // YouTube users often search with detailed queries
        const youtubeKeywords = topKeywords
          .filter((keyword: any) => {
            const keywordName = (keyword.name || '').toLowerCase();
            // YouTube-specific keyword filtering - prefer longer, descriptive keywords
            const words = keywordName.split(' ');
            return (
              words.length > 1 || // Multi-word keywords
              // Or contains affiliate name/domain
              (affiliateName && keywordName.includes(affiliateName)) ||
              (domainBase && keywordName.includes(domainBase)) ||
              (domain && keywordName.includes(domain))
            );
          })
          .map((keyword: any) => keyword.name)
          .filter(Boolean);

        relevantKeywords = [...relevantKeywords, ...youtubeKeywords];
        console.log(`Selected ${youtubeKeywords.length} YouTube-optimized keywords`);
      }

      if (platform === 'tiktok' || platform === 'all') {
        // For TikTok, prioritize shorter, trending keywords
        // TikTok users often search with shorter, trendy terms
        const tiktokKeywords = topKeywords
          .filter((keyword: any) => {
            const keywordName = (keyword.name || '').toLowerCase();
            // TikTok-specific keyword filtering - prefer shorter, higher traffic keywords
            const isShort = keywordName.split(' ').length <= 3;
            const highTraffic = keyword.traffic > 10000;

            return (
              (isShort && highTraffic) ||
              // Or contains affiliate name/domain
              (affiliateName && keywordName.includes(affiliateName)) ||
              (domainBase && keywordName.includes(domainBase)) ||
              (domain && keywordName.includes(domain))
            );
          })
          .map((keyword: any) => keyword.name)
          .filter(Boolean);

        relevantKeywords = [...relevantKeywords, ...tiktokKeywords];
        console.log(`Selected ${tiktokKeywords.length} TikTok-optimized keywords`);
      }

      // Remove duplicates
      const allKeywords = [...new Set(relevantKeywords)];

      console.log(`Final ${platform} keywords: ${allKeywords.join(', ')}`);
      return allKeywords;
    } catch (error) {
      console.error(`Error getting ${platform} keywords from traffic web:`, error);
      return [];
    }
  },

  /**
   * Search for trending ads using multiple keywords
   * @param {string} platform - Platform to search (youtube or tiktok)
   * @param {string[]} keywords - Array of keywords to search for
   * @param {object} params - Optional parameters for the search
   * @param {number} affiliateId - Optional affiliate ID to associate with the ads
   * @returns {object} The combined search results
   */
  async searchWithMultipleKeywords(
    platform: string,
    keywords: string[],
    params?: any,
    affiliateId?: number
  ) {
    console.log(`Searching ${platform} with multiple keywords: ${keywords.join(', ')}`);

    // Process each keyword sequentially
    const searchResults = [];
    for (const keyword of keywords) {
      console.log(
        `Processing keyword "${keyword}" for ${platform} (${searchResults.length + 1}/${keywords.length})`
      );

      try {
        let result;
        if (platform === 'youtube') {
          result = await this.searchYoutubeAds(keyword, params, affiliateId);
        } else if (platform === 'tiktok') {
          result = await this.searchTiktokAds(keyword, params, affiliateId);
        } else {
          result = {
            success: false,
            message: `Unsupported platform: ${platform}`,
            data: null,
          };
        }

        searchResults.push({
          keyword,
          ...result,
        });

        console.log(
          `Completed search for "${keyword}" on ${platform}: ${result.success ? 'success' : 'failed'}`
        );
      } catch (error) {
        console.error(`Error searching ${platform} ads with keyword "${keyword}":`, error);
        searchResults.push({
          keyword,
          success: false,
          message: `Error searching ${platform} ads: ${error.message}`,
          error,
        });
      }

      // Small delay between keyword searches to avoid overwhelming APIs
      if (keywords.indexOf(keyword) < keywords.length - 1) {
        console.log('Short pause between keyword searches...');
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    // Combine all search results
    const allSucceeded = searchResults.some((result) => result.success);
    const totalCreated = searchResults.reduce(
      (sum, result) => sum + (result.data?.created || 0),
      0
    );
    const totalUpdated = searchResults.reduce(
      (sum, result) => sum + (result.data?.updated || 0),
      0
    );

    return {
      success: allSucceeded,
      message: `Completed search with ${keywords.length} keywords on ${platform}: created ${totalCreated}, updated ${totalUpdated}`,
      data: {
        keywords: searchResults,
        created: totalCreated,
        updated: totalUpdated,
      },
    };
  },

  /**
   * Search for trending ads across multiple platforms
   * @param {string[]} platforms - Array of platforms to search
   * @param {string} keyword - Optional keyword to search for
   * @param {object} params - Optional parameters for the search
   * @param {number} affiliateId - Optional affiliate ID to associate with the ads
   * @param {object} affiliate - Optional affiliate object containing platform-specific keywords
   * @returns {object} The combined search results
   */
  async searchMultiplePlatforms(
    platforms: string[],
    keyword?: string,
    params?: any,
    affiliateId?: number,
    affiliate?: any
  ) {
    console.log(`Searching across multiple platforms: ${platforms.join(', ')}`);

    const searchResults = await Promise.all(
      platforms.map(async (platform) => {
        // Create a new params object with the single platform
        const platformParams = { ...params };
        delete platformParams.platform; // Remove the platform array to avoid recursion

        try {
          let result;

          switch (platform.toLowerCase()) {
            case 'youtube':
              // Handle multiple YouTube keywords
              const youtubeKeywords = affiliate?.youtube_ad_keyword
                ? this.splitKeywords(affiliate.youtube_ad_keyword)
                : [];

              // If no specific YouTube keywords, try getting from traffic web
              // if (youtubeKeywords.length === 0 && affiliate) {
              //   // Get YouTube-specific keywords from traffic web
              //   const trafficWebKeywords = await this.getAffiliateKeywordsFromTrafficWeb(
              //     affiliate,
              //     'youtube'
              //   );
              //   if (trafficWebKeywords.length > 0) {
              //     console.log(
              //       `Using YouTube-specific traffic web keywords: ${trafficWebKeywords.join(', ')}`
              //     );
              //     youtubeKeywords = trafficWebKeywords;
              //   }
              // }

              // Fallback to URL domain if no keywords available
              if (youtubeKeywords.length === 0 && affiliate?.url) {
                try {
                  const parsedUrl = new URL(
                    affiliate.url.startsWith('http') ? affiliate.url : `https://${affiliate.url}`
                  );
                  const domainKeyword = parsedUrl.hostname.replace(/^www\./, '');
                  console.log(
                    `Extracted domain keyword for YouTube: ${domainKeyword} from URL: ${affiliate.url}`
                  );
                  youtubeKeywords.push(domainKeyword);
                } catch (e) {
                  console.error('Error parsing URL for YouTube keyword:', e);
                }
              }

              // Fallback to generic keyword if still no keywords
              if (youtubeKeywords.length === 0 && keyword) {
                youtubeKeywords.push(keyword);
              }

              if (youtubeKeywords.length > 0) {
                console.log(`Using YouTube keywords: ${youtubeKeywords.join(', ')}`);
                if (youtubeKeywords.length > 1) {
                  result = await this.searchWithMultipleKeywords(
                    'youtube',
                    youtubeKeywords,
                    platformParams,
                    affiliateId
                  );
                } else {
                  result = await this.searchYoutubeAds(
                    youtubeKeywords[0],
                    platformParams,
                    affiliateId
                  );
                }
              } else {
                console.log('Skipping YouTube search - no keyword available');
                result = { success: false, message: 'No YouTube keyword available', data: null };
              }
              break;

            case 'tiktok':
              // Handle multiple TikTok keywords
              let tiktokKeywords = affiliate?.tiktok_ad_keyword
                ? this.splitKeywords(affiliate.tiktok_ad_keyword)
                : [];

              // If no specific TikTok keywords, try getting from traffic web
              if (tiktokKeywords.length === 0 && affiliate) {
                // Get TikTok-specific keywords from traffic web
                const trafficWebKeywords = await this.getAffiliateKeywordsFromTrafficWeb(
                  affiliate,
                  'tiktok'
                );
                if (trafficWebKeywords.length > 0) {
                  console.log(
                    `Using TikTok-specific traffic web keywords: ${trafficWebKeywords.join(', ')}`
                  );
                  tiktokKeywords = trafficWebKeywords;
                }
              }

              // Fallback to URL domain if no keywords available
              if (tiktokKeywords.length === 0 && affiliate?.url) {
                try {
                  const parsedUrl = new URL(
                    affiliate.url.startsWith('http') ? affiliate.url : `https://${affiliate.url}`
                  );
                  tiktokKeywords.push(parsedUrl.hostname.replace(/^www\./, ''));
                } catch (e) {
                  console.error('Error parsing URL for TikTok keyword:', e);
                }
              }

              // Fallback to generic keyword if still no keywords
              if (tiktokKeywords.length === 0 && keyword) {
                tiktokKeywords.push(keyword);
              }

              if (tiktokKeywords.length > 0) {
                console.log(`Using TikTok keywords: ${tiktokKeywords.join(', ')}`);
                if (tiktokKeywords.length > 1) {
                  result = await this.searchWithMultipleKeywords(
                    'tiktok',
                    tiktokKeywords,
                    platformParams,
                    affiliateId
                  );
                } else {
                  result = await this.searchTiktokAds(
                    tiktokKeywords[0],
                    platformParams,
                    affiliateId
                  );
                }
              } else {
                console.log('Skipping TikTok search - no keyword available');
                result = { success: false, message: 'No TikTok keyword available', data: null };
              }
              break;

            default:
              result = {
                success: false,
                message: `Unsupported platform: ${platform}`,
                data: null,
              };
          }

          return {
            platform,
            ...result,
          };
        } catch (error) {
          console.error(`Error searching ${platform} ads:`, error);
          return {
            platform,
            success: false,
            message: `Error searching ${platform} ads: ${error.message}`,
            error,
          };
        }
      })
    );

    // Combine all search results
    const allSucceeded = searchResults.some((result) => result.success);
    const totalCreated = searchResults.reduce(
      (sum, result) => sum + (result.data?.created || 0),
      0
    );
    const totalUpdated = searchResults.reduce(
      (sum, result) => sum + (result.data?.updated || 0),
      0
    );

    return {
      success: allSucceeded,
      message: `Completed search across ${platforms.length} platforms: created ${totalCreated}, updated ${totalUpdated}`,
      data: {
        platforms: searchResults,
        created: totalCreated,
        updated: totalUpdated,
      },
    };
  },

  /**
   * Search for trending ads for an affiliate across TikTok and YouTube
   * @param {object} affiliate - The affiliate object to search ads for
   * @param {object} params - Optional parameters for the search
   * @returns {object} The search results
   */
  async searchTrendingAds(affiliate: any, params?: any) {
    try {
      if (!affiliate) {
        return {
          success: false,
          message: 'No affiliate provided for trending ads search',
          data: null,
        };
      }

      // Get affiliate ID
      const affiliateId = affiliate.id;

      // Extract keyword from affiliate URL
      let keyword: string | undefined = undefined;
      const url = affiliate.url || affiliate.airtable_data?.user_ref_link;

      if (url) {
        try {
          const parsedUrl = new URL(url.startsWith('http') ? url : `https://${url}`);
          keyword = parsedUrl.hostname.replace(/^www\./, '');
          console.log(`Extracted domain keyword from URL: ${keyword}`);
        } catch (error) {
          console.error(`Error parsing URL ${url}:`, error);
        }
      }

      // Set default params if not provided
      params = params || { limit: 20 };

      // Always search both platforms
      const platforms = ['tiktok', 'youtube'];

      // Track used keywords for logging
      const usedKeywords: Record<string, string[]> = {
        domain: keyword ? [keyword] : [],
        youtube: [],
        tiktok: [],
      };

      // Search across all platforms
      const result = await this.searchMultiplePlatforms(
        platforms,
        keyword,
        params,
        affiliateId,
        affiliate
      );

      // Update used keywords tracking from the result with separate platform keywords
      platforms.forEach((platform) => {
        const platformResult = result.data?.platforms?.find((p) => p.platform === platform);
        if (platformResult) {
          if (platformResult.data?.keywords) {
            usedKeywords[platform] = platformResult.data.keywords.map((k) => k.keyword);
          } else if (platform === 'youtube' || platform === 'tiktok') {
            // For backwards compatibility with results that don't have keywords array
            usedKeywords[platform] = affiliate[`${platform}_ad_keyword`]
              ? this.splitKeywords(affiliate[`${platform}_ad_keyword`])
              : [];
          }
        }
      });

      // Create a log entry
      if (affiliateId) {
        // Format the keywords for logging
        let keywordsLog = '';
        if (usedKeywords.domain.length > 0) {
          keywordsLog += 'Domain: ' + usedKeywords.domain.join(', ');
        }
        if (usedKeywords.youtube.length > 0) {
          if (keywordsLog) keywordsLog += ', ';
          keywordsLog += 'YouTube: ' + usedKeywords.youtube.join(', ');
        }
        if (usedKeywords.tiktok.length > 0) {
          if (keywordsLog) keywordsLog += ', ';
          keywordsLog += 'TikTok: ' + usedKeywords.tiktok.join(', ');
        }

        try {
          await strapi.documents('api::top-ad-log.top-ad-log').create({
            data: {
              keyword: keywordsLog || 'No keywords used',
              affiliate: affiliateId,
              result: result,
            },
          });
          console.log(`Saved search log for affiliate: ${affiliate.name || affiliateId}`);
        } catch (logError) {
          console.error('Error creating top-ad-log:', logError);
        }
      }

      return result;
    } catch (error) {
      console.error('Error in searchTrendingAds:', error);
      return {
        success: false,
        message: `Error in searchTrendingAds: ${error.message}`,
        error,
      };
    }
  },

  /**
   * Search for TikTok ads and store them in the database
   * @param {string} keyword - Optional keyword to search for
   * @param {object} params - Optional parameters for the search
   * @param {number} affiliateId - Optional affiliate ID to associate with the ads
   * @returns {object} The search results
   */
  async searchTiktokAds(keyword?: string, params?: any, affiliateId?: number) {
    try {
      // Setup default search parameters
      const searchParams = {
        page: params?.page || 1,
        limit: params?.limit || 20,
        period: params?.period || 180,
        country: params?.country || 'US',
        order_by: params?.order_by || 'ctr',
      };

      // Add keyword if provided
      if (keyword) {
        searchParams['keyword'] = keyword;
      }

      // Fetch ads from TikTok Creative Center API
      console.log('Searching TikTok trending ads with params:', searchParams);
      const response = (await TiktokCreativeCenterClient.searchAds(
        searchParams
      )) as ITikTokAdResponse;

      if (response.code !== 0 || !response.data?.materials) {
        console.error('Failed to fetch TikTok trending ads', response);
        return { success: false, message: 'Failed to fetch TikTok trending ads', data: null };
      }

      // Get all unique ad IDs
      const adIds = response.data.materials.map((ad) => ad.id);
      console.log(`Found ${adIds.length} TikTok ads to process`);

      // Check which ads already exist in the database
      const existingAds = await strapi.documents('api::top-ad.top-ad').findMany({
        filters: { ad_id: { $in: adIds }, platform: 'tiktok' },
        fields: ['id', 'ad_id', 'last_fetched'],
      });

      // Create a map of existing ads for quick lookup
      const existingAdsMap = new Map();
      existingAds.forEach((ad) => {
        existingAdsMap.set(ad.ad_id, ad);
      });

      // Prepare arrays for bulk create and update operations
      const adsToCreate = [];
      const adsToUpdate = [];

      // Create a rate-limited version of the getAdDetail method
      const rateLimit = 3; // 3 requests per second
      console.log(`Setting up rate limit of ${rateLimit} requests per second for TikTok API`);
      const getAdDetailRateLimited = this.rateLimiter(
        TiktokCreativeCenterClient.getAdDetail.bind(TiktokCreativeCenterClient),
        rateLimit
      );

      // Process ads in batches to control rate
      const batchSize = 10; // Process 10 ads at a time
      console.log(`Processing TikTok ads in batches of ${batchSize}`);

      for (let i = 0; i < response.data.materials.length; i += batchSize) {
        const batch = response.data.materials.slice(i, i + batchSize);
        console.log(
          `Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(response.data.materials.length / batchSize)}`
        );

        // Process each batch with limited concurrency
        await Promise.all(
          batch.map(async (adItem) => {
            const adId = adItem.id;
            const existingAd = existingAdsMap.get(adId);

            // Skip fetching details if ad was updated less than 24 hours ago
            if (existingAd) {
              const lastFetched = new Date(existingAd.last_fetched || 0);
              const now = new Date();
              const hoursSinceLastFetch =
                (now.getTime() - lastFetched.getTime()) / (1000 * 60 * 60);

              if (hoursSinceLastFetch < 24) {
                console.log(
                  `Skipping TikTok ad ${adId} - was updated ${hoursSinceLastFetch.toFixed(2)} hours ago`
                );
                return;
              }
            }

            try {
              // Fetch detailed information for the ad with rate limiting
              console.log(`Fetching details for TikTok ad ${adId}`);
              const detailResponse = (await getAdDetailRateLimited(
                adId
              )) as ITikTokAdDetailResponse;

              if (detailResponse.code !== 0 || !detailResponse.data) {
                console.error(`Failed to fetch details for TikTok ad ${adId}`, detailResponse);
                return;
              }

              // Parse the ad data
              const parsedAd = this.parseTikTokAd(detailResponse.data, keyword);

              // Add to appropriate array for bulk operation
              if (existingAd) {
                const updateData: any = {
                  documentId: existingAd.id,
                  data: {
                    ...parsedAd,
                    last_fetched: new Date(),
                  },
                };

                // Add affiliate relation if provided - Fix the relation structure
                if (affiliateId) {
                  console.log(`Linking TikTok ad ${adId} to affiliate ${affiliateId}`);
                  updateData.data.affiliate = affiliateId;
                }

                adsToUpdate.push(updateData);
              } else {
                const createData: any = {
                  ...parsedAd,
                  last_fetched: new Date(),
                };

                // Add affiliate relation if provided - Fix the relation structure
                if (affiliateId) {
                  console.log(`Linking new TikTok ad ${adId} to affiliate ${affiliateId}`);
                  createData.affiliate = affiliateId;
                }

                adsToCreate.push(createData);
              }
            } catch (error) {
              console.error(`Error processing TikTok ad ${adId}:`, error);
            }
          })
        );

        // Small delay between batches to avoid overwhelming the system
        if (i + batchSize < response.data.materials.length) {
          console.log('Short pause between batches...');
          await new Promise((resolve) => setTimeout(resolve, 500));
        }
      }

      console.log(
        `Prepared ${adsToCreate.length} TikTok ads to create and ${adsToUpdate.length} ads to update`
      );

      // Perform bulk operations
      let createdCount = 0;
      let updatedCount = 0;

      // Bulk create with direct database transaction
      if (adsToCreate.length > 0) {
        const startTime = Date.now();
        console.log(`Starting bulk creation of ${adsToCreate.length} TikTok ads`);

        // Use optimal batch size based on testing
        const batchSize = 50;
        const totalBatches = Math.ceil(adsToCreate.length / batchSize);

        console.log(`Will create in ${totalBatches} batches of up to ${batchSize} ads each`);

        // Process in smaller batches to avoid overwhelming the database
        for (let i = 0; i < adsToCreate.length; i += batchSize) {
          const batchStartTime = Date.now();
          const currentBatch = adsToCreate.slice(i, i + batchSize);
          const batchNum = Math.floor(i / batchSize) + 1;

          console.log(
            `Processing create batch ${batchNum}/${totalBatches} with ${currentBatch.length} ads`
          );

          try {
            await strapi.db.connection.transaction(async (trx) => {
              // Prepare data for batch insertion
              const recordsToInsert = currentBatch.map((adData) => {
                // Generate document ID using the same function as social-listening
                const documentId = this.generateUniqueId();

                // Remove affiliate from the main record - we'll handle it separately
                const { affiliate, ...recordData } = adData;

                return {
                  document_id: documentId,
                  created_at: new Date(),
                  updated_at: new Date(),
                  published_at: new Date(),
                  // Include all ad data fields
                  ad_id: recordData.ad_id,
                  ad_title: recordData.ad_title || '',
                  brand_name: recordData.brand_name || '',
                  platform: recordData.platform,
                  is_displayed: recordData.is_displayed,
                  keyword: recordData.keyword || '',
                  country_code: JSON.stringify(recordData.country_code || []),
                  objectives: JSON.stringify(recordData.objectives || []),
                  video_info: JSON.stringify(recordData.video_info || {}),
                  landing_page: recordData.landing_page || '',
                  // Other fields
                  ctr: recordData.ctr || 0,
                  cost: recordData.cost || 0,
                  likes: recordData.like || 0,
                  comments: recordData.comment || 0,
                  shares: recordData.share || 0,
                  industry_key: recordData.industry_key || '',
                  objective_key: recordData.objective_key || '',
                  source: recordData.source || '',
                  is_search: recordData.is_search || false,
                  favorite: recordData.favorite || false,
                  last_fetched: new Date(),
                };
              });

              // Insert all records in a single transaction
              const insertedRecords = await strapi.db
                .connection('top_ads')
                .insert(recordsToInsert)
                .returning(['id', 'document_id'])
                .onConflict(['ad_id']) // Add conflict handling for unique ad_id
                .ignore() // Ignore the insert if ad_id already exists
                .transacting(trx);

              createdCount += insertedRecords.length;
              console.log(`Inserted ${insertedRecords.length} records in transaction`);

              // If an affiliate ID was provided, create the relationships
              if (affiliateId && insertedRecords.length > 0) {
                // Create relationship records for the junction table
                const affiliateLinks = insertedRecords.map((record) => ({
                  top_ad_id: record.id,
                  affiliate_id: affiliateId,
                }));

                // Insert the relationships
                await strapi.db
                  .connection('top_ads_affiliate_lnk')
                  .insert(affiliateLinks)
                  .onConflict(['top_ad_id', 'affiliate_id'])
                  .ignore()
                  .transacting(trx);

                console.log(`Created ${affiliateLinks.length} affiliate relationships`);
              }
            });

            const batchDuration = (Date.now() - batchStartTime) / 1000;
            console.log(`Batch ${batchNum} completed in ${batchDuration.toFixed(2)}s`);
          } catch (error) {
            console.error(`Error in create batch ${batchNum}:`, error);
          }

          // Add a small delay between batches to prevent database overload
          if (i + batchSize < adsToCreate.length) {
            await new Promise((resolve) => setTimeout(resolve, 100));
          }
        }

        const totalDuration = (Date.now() - startTime) / 1000;
        console.log(
          `Created ${createdCount}/${adsToCreate.length} new TikTok ads in ${totalDuration.toFixed(2)}s`
        );
      }

      // Bulk update with direct database transaction
      if (adsToUpdate.length > 0) {
        const startTime = Date.now();
        console.log(`Starting bulk update of ${adsToUpdate.length} TikTok ads`);

        // Use optimal batch size based on testing
        const batchSize = 50;
        const totalBatches = Math.ceil(adsToUpdate.length / batchSize);

        console.log(`Will update in ${totalBatches} batches of up to ${batchSize} ads each`);

        // Process in smaller batches to avoid overwhelming the database
        for (let i = 0; i < adsToUpdate.length; i += batchSize) {
          const batchStartTime = Date.now();
          const currentBatch = adsToUpdate.slice(i, i + batchSize);
          const batchNum = Math.floor(i / batchSize) + 1;

          console.log(
            `Processing update batch ${batchNum}/${totalBatches} with ${currentBatch.length} ads`
          );

          try {
            // Group updates by common fields to optimize updates
            const batchSuccess = await strapi.db.connection.transaction(async (trx) => {
              let successCount = 0;
              const updatedIds = [];

              // Process each item individually for updates
              for (const { documentId, data } of currentBatch) {
                try {
                  // Remove affiliate from data - we'll handle it separately
                  const { affiliate, ...updateData } = data;

                  // Store additional fields in video_info to match schema
                  const enhancedVideoInfo = {
                    ...updateData.video_info,
                    description: updateData.description || '',
                    cta_button: updateData.cta_button || '',
                    networks: updateData.networks || [],
                    devices: updateData.devices || [],
                    publishers_count: updateData.publishers_count || 0,
                    tracking_tools: updateData.tracking_tools || [],
                    aff_networks: updateData.aff_networks || [],
                    first_seen: updateData.first_seen || '',
                    last_seen: updateData.last_seen || '',
                  };

                  // Get the record ID from the documentId
                  const record = await strapi.db
                    .connection('top_ads')
                    .where({ document_id: documentId })
                    .select('id')
                    .first()
                    .transacting(trx);

                  if (!record) {
                    console.error(`Record with document_id ${documentId} not found`);
                    continue;
                  }

                  // Update record with direct SQL - only schema-defined fields
                  const updateResult = await strapi.db
                    .connection('top_ads')
                    .where({ document_id: documentId })
                    .update({
                      updated_at: new Date(),
                      ad_title: updateData.ad_title || '',
                      brand_name: updateData.brand_name || '',
                      country_code: JSON.stringify(updateData.country_code || []),
                      video_info: JSON.stringify(enhancedVideoInfo),
                      // Schema-defined fields
                      views: updateData.views || 0,
                      likes: updateData.like || 0,
                      comments: updateData.comment || 0,
                      shares: updateData.share || 0,
                      ctr: updateData.ctr || 0,
                      cost: updateData.cost || 0,
                      industry_key: updateData.industry_key || '',
                      objective_key: updateData.objective_key || '',
                      objectives: JSON.stringify(updateData.objectives || []),
                      landing_page: updateData.landing_page || '',
                      source: updateData.source || '',
                      is_search: updateData.is_search || false,
                      favorite: updateData.favorite || false,
                      last_fetched: new Date(),
                    })
                    .transacting(trx);

                  if (updateResult > 0) {
                    successCount++;
                    updatedIds.push(record.id);
                  }
                } catch (err) {
                  console.error(`Error updating individual record ${documentId}:`, err);
                }
              }

              // If an affiliate ID was provided, create or update the relationships
              if (affiliateId && updatedIds.length > 0) {
                // Create relationship records for the junction table
                const affiliateLinks = updatedIds.map((id) => ({
                  top_ad_id: id,
                  affiliate_id: affiliateId,
                }));

                // Insert the relationships
                await strapi.db
                  .connection('top_ads_affiliate_lnk')
                  .insert(affiliateLinks)
                  .onConflict(['top_ad_id', 'affiliate_id'])
                  .ignore()
                  .transacting(trx);

                console.log(`Updated ${affiliateLinks.length} affiliate relationships`);
              }

              return successCount;
            });

            updatedCount += batchSuccess;

            const batchDuration = (Date.now() - batchStartTime) / 1000;
            console.log(
              `Batch ${batchNum} completed in ${batchDuration.toFixed(2)}s: updated ${batchSuccess} ads`
            );
          } catch (error) {
            console.error(`Error in update batch ${batchNum}:`, error);
          }

          // Add a small delay between batches to prevent database overload
          if (i + batchSize < adsToUpdate.length) {
            await new Promise((resolve) => setTimeout(resolve, 100));
          }
        }

        const totalDuration = (Date.now() - startTime) / 1000;
        console.log(
          `Updated ${updatedCount}/${adsToUpdate.length} TikTok ads in ${totalDuration.toFixed(2)}s`
        );
      }

      return {
        success: true,
        message: `Successfully processed TikTok ads: created ${createdCount}, updated ${updatedCount}`,
        data: {
          created: createdCount,
          updated: updatedCount,
          pagination: response.data.pagination,
        },
      };
    } catch (error) {
      console.error('Error processing TikTok trending ads:', error);
      return {
        success: false,
        message: `Error processing TikTok trending ads: ${error.message}`,
        error,
      };
    }
  },

  /**
   * Search for YouTube ads using Adplexity and store them in the database
   * @param {string} keyword - Optional keyword to search for
   * @param {object} params - Optional parameters for the search
   * @param {number} affiliateId - Optional affiliate ID to associate with the ads
   * @returns {object} The search results
   */
  async searchYoutubeAds(keyword?: string, params?: any, affiliateId?: number) {
    try {
      console.log('Searching YouTube ads with keyword:', keyword);

      // Setup search parameters for Adplexity
      const searchParams = {
        query: keyword || 'adcreative',
        daysRunningFrom: params?.period || 30,
        limit: params?.limit || 20,
        offset: ((params?.page || 1) - 1) * (params?.limit || 20),
      };

      // Call Adplexity client to search YouTube ads
      const response = (await AdplexityClient.searchYoutube(
        searchParams.query,
        searchParams
      )) as IYoutubeAdResponse;

      if (!response || !response.records || !Array.isArray(response.records)) {
        console.error('Failed to fetch YouTube ads', response);
        return { success: false, message: 'Failed to fetch YouTube ads', data: null };
      }

      // Get all ad IDs
      const adIds = response.records.map((ad) => ad.id);
      console.log(`Found ${adIds.length} YouTube ads to process`);

      // Check which ads already exist in the database
      const existingAds = await strapi.documents('api::top-ad.top-ad').findMany({
        filters: { ad_id: { $in: adIds }, platform: 'youtube' },
        fields: ['id', 'ad_id', 'last_fetched'],
      });

      // Create a map of existing ads for quick lookup
      const existingAdsMap = new Map();
      existingAds.forEach((ad) => {
        existingAdsMap.set(ad.ad_id, ad);
      });

      // Prepare arrays for bulk create and update operations
      const adsToCreate = [];
      const adsToUpdate = [];

      // Process each ad from the response
      for (const adItem of response.records) {
        if (!adItem.id || !adItem.youtube_id) continue;
        const adId = adItem.id;
        const existingAd = existingAdsMap.get(adId);

        // Skip processing if ad was updated less than 24 hours ago
        if (existingAd) {
          const lastFetched = new Date(existingAd.last_fetched || 0);
          const now = new Date();
          const hoursSinceLastFetch = (now.getTime() - lastFetched.getTime()) / (1000 * 60 * 60);

          if (hoursSinceLastFetch < 24) {
            console.log(
              `Skipping YouTube ad ${adId} - was updated ${hoursSinceLastFetch.toFixed(2)} hours ago`
            );
            continue;
          }
        }

        try {
          // Parse ad data
          const parsedAd = this.parseYoutubeAd(adItem, keyword);

          // Add to appropriate array for bulk operation
          if (existingAd) {
            const updateData: any = {
              documentId: existingAd.id,
              data: {
                ...parsedAd,
                last_fetched: new Date(),
              },
            };

            // Add affiliate relation if provided - Fix the relation structure
            if (affiliateId) {
              console.log(`Linking YouTube ad ${adId} to affiliate ${affiliateId}`);
              updateData.data.affiliate = affiliateId;
            }

            adsToUpdate.push(updateData);
          } else {
            const createData: any = {
              ...parsedAd,
              last_fetched: new Date(),
            };

            // Add affiliate relation if provided - Fix the relation structure
            if (affiliateId) {
              console.log(`Linking new YouTube ad ${adId} to affiliate ${affiliateId}`);
              createData.affiliate = affiliateId;
            }

            adsToCreate.push(createData);
          }
        } catch (error) {
          console.error(`Error processing YouTube ad ${adId}:`, error);
        }
      }

      console.log(
        `Prepared ${adsToCreate.length} YouTube ads to create and ${adsToUpdate.length} ads to update`
      );

      // Perform bulk operations
      let createdCount = 0;
      let updatedCount = 0;

      // Bulk create with direct database transaction
      if (adsToCreate.length > 0) {
        const startTime = Date.now();
        console.log(`Starting bulk creation of ${adsToCreate.length} YouTube ads`);

        // Use optimal batch size based on testing
        const batchSize = 50;
        const totalBatches = Math.ceil(adsToCreate.length / batchSize);

        console.log(`Will create in ${totalBatches} batches of up to ${batchSize} ads each`);

        // Process in smaller batches to avoid overwhelming the database
        for (let i = 0; i < adsToCreate.length; i += batchSize) {
          const batchStartTime = Date.now();
          const currentBatch = adsToCreate.slice(i, i + batchSize);
          const batchNum = Math.floor(i / batchSize) + 1;

          console.log(
            `Processing create batch ${batchNum}/${totalBatches} with ${currentBatch.length} ads`
          );

          try {
            await strapi.db.connection.transaction(async (trx) => {
              // Prepare data for batch insertion
              const recordsToInsert = currentBatch.map((adData) => {
                // Generate document ID using the same function as social-listening
                const documentId = this.generateUniqueId();

                // Remove affiliate from the main record - we'll handle it separately
                const { affiliate, ...recordData } = adData;

                // Store additional fields in video_info to match schema
                const enhancedVideoInfo = {
                  ...recordData.video_info,
                  description: recordData.description || '',
                  cta_button: recordData.cta_button || '',
                  networks: recordData.networks || [],
                  devices: recordData.devices || [],
                  publishers_count: recordData.publishers_count || 0,
                  tracking_tools: recordData.tracking_tools || [],
                  aff_networks: recordData.aff_networks || [],
                  first_seen: recordData.first_seen || '',
                  last_seen: recordData.last_seen || '',
                };

                return {
                  document_id: documentId,
                  created_at: new Date(),
                  updated_at: new Date(),
                  published_at: new Date(),
                  // Include only schema-defined fields
                  ad_id: recordData.ad_id,
                  ad_title: recordData.ad_title || '',
                  brand_name: recordData.brand_name || '',
                  platform: recordData.platform,
                  is_displayed: recordData.is_displayed,
                  keyword: recordData.keyword || '',
                  country_code: JSON.stringify(recordData.country_code || []),
                  video_info: JSON.stringify(enhancedVideoInfo),
                  // Other schema-defined fields
                  views: recordData.views || 0,
                  likes: recordData.like || 0,
                  ctr: recordData.ctr || 0,
                  cost: recordData.cost || 0,
                  shares: recordData.share || 0,
                  comments: recordData.comment || 0,
                  industry_key: recordData.industry_key || '',
                  objective_key: recordData.objective_key || '',
                  objectives: JSON.stringify(recordData.objectives || []),
                  landing_page: recordData.landing_page || '',
                  source: recordData.source || '',
                  is_search: recordData.is_search || false,
                  favorite: recordData.favorite || false,
                  last_fetched: new Date(),
                };
              });

              // Insert all records in a single transaction
              const insertedRecords = await strapi.db
                .connection('top_ads')
                .insert(recordsToInsert)
                .returning(['id', 'document_id'])
                .onConflict(['ad_id']) // Add conflict handling for unique ad_id
                .ignore() // Ignore the insert if ad_id already exists
                .transacting(trx);

              createdCount += insertedRecords.length;
              console.log(`Inserted ${insertedRecords.length} records in transaction`);

              // If an affiliate ID was provided, create the relationships
              if (affiliateId && insertedRecords.length > 0) {
                // Create relationship records for the junction table
                const affiliateLinks = insertedRecords.map((record) => ({
                  top_ad_id: record.id,
                  affiliate_id: affiliateId,
                }));

                // Insert the relationships
                await strapi.db
                  .connection('top_ads_affiliate_lnk')
                  .insert(affiliateLinks)
                  .onConflict(['top_ad_id', 'affiliate_id'])
                  .ignore()
                  .transacting(trx);

                console.log(`Created ${affiliateLinks.length} affiliate relationships`);
              }
            });

            const batchDuration = (Date.now() - batchStartTime) / 1000;
            console.log(`Batch ${batchNum} completed in ${batchDuration.toFixed(2)}s`);
          } catch (error) {
            console.error(`Error in create batch ${batchNum}:`, error);
          }

          // Add a small delay between batches to prevent database overload
          if (i + batchSize < adsToCreate.length) {
            await new Promise((resolve) => setTimeout(resolve, 100));
          }
        }

        const totalDuration = (Date.now() - startTime) / 1000;
        console.log(
          `Created ${createdCount}/${adsToCreate.length} new YouTube ads in ${totalDuration.toFixed(2)}s`
        );
      }

      // Bulk update with direct database transaction
      if (adsToUpdate.length > 0) {
        const startTime = Date.now();
        console.log(`Starting bulk update of ${adsToUpdate.length} YouTube ads`);

        // Use optimal batch size based on testing
        const batchSize = 50;
        const totalBatches = Math.ceil(adsToUpdate.length / batchSize);

        console.log(`Will update in ${totalBatches} batches of up to ${batchSize} ads each`);

        // Process in smaller batches to avoid overwhelming the database
        for (let i = 0; i < adsToUpdate.length; i += batchSize) {
          const batchStartTime = Date.now();
          const currentBatch = adsToUpdate.slice(i, i + batchSize);
          const batchNum = Math.floor(i / batchSize) + 1;

          console.log(
            `Processing update batch ${batchNum}/${totalBatches} with ${currentBatch.length} ads`
          );

          try {
            // Group updates by common fields to optimize updates
            const batchSuccess = await strapi.db.connection.transaction(async (trx) => {
              let successCount = 0;
              const updatedIds = [];

              // Process each item individually for updates
              for (const { documentId, data } of currentBatch) {
                try {
                  // Remove affiliate from data - we'll handle it separately
                  const { affiliate, ...updateData } = data;

                  // Store additional fields in video_info to match schema
                  const enhancedVideoInfo = {
                    ...updateData.video_info,
                    description: updateData.description || '',
                    cta_button: updateData.cta_button || '',
                    networks: updateData.networks || [],
                    devices: updateData.devices || [],
                    publishers_count: updateData.publishers_count || 0,
                    tracking_tools: updateData.tracking_tools || [],
                    aff_networks: updateData.aff_networks || [],
                    first_seen: updateData.first_seen || '',
                    last_seen: updateData.last_seen || '',
                  };

                  // Get the record ID from the documentId
                  const record = await strapi.db
                    .connection('top_ads')
                    .where({ document_id: documentId })
                    .select('id')
                    .first()
                    .transacting(trx);

                  if (!record) {
                    console.error(`Record with document_id ${documentId} not found`);
                    continue;
                  }

                  // Update record with direct SQL - only schema-defined fields
                  const updateResult = await strapi.db
                    .connection('top_ads')
                    .where({ document_id: documentId })
                    .update({
                      updated_at: new Date(),
                      ad_title: updateData.ad_title || '',
                      brand_name: updateData.brand_name || '',
                      country_code: JSON.stringify(updateData.country_code || []),
                      video_info: JSON.stringify(enhancedVideoInfo),
                      // Schema-defined fields
                      views: updateData.views || 0,
                      likes: updateData.like || 0,
                      comments: updateData.comment || 0,
                      shares: updateData.share || 0,
                      ctr: updateData.ctr || 0,
                      cost: updateData.cost || 0,
                      industry_key: updateData.industry_key || '',
                      objective_key: updateData.objective_key || '',
                      objectives: JSON.stringify(updateData.objectives || []),
                      landing_page: updateData.landing_page || '',
                      source: updateData.source || '',
                      is_search: updateData.is_search || false,
                      favorite: updateData.favorite || false,
                      last_fetched: new Date(),
                    })
                    .transacting(trx);

                  if (updateResult > 0) {
                    successCount++;
                    updatedIds.push(record.id);
                  }
                } catch (err) {
                  console.error(`Error updating individual record ${documentId}:`, err);
                }
              }

              // If an affiliate ID was provided, create or update the relationships
              if (affiliateId && updatedIds.length > 0) {
                // Create relationship records for the junction table
                const affiliateLinks = updatedIds.map((id) => ({
                  top_ad_id: id,
                  affiliate_id: affiliateId,
                }));

                // Insert the relationships
                await strapi.db
                  .connection('top_ads_affiliate_lnk')
                  .insert(affiliateLinks)
                  .onConflict(['top_ad_id', 'affiliate_id'])
                  .ignore()
                  .transacting(trx);

                console.log(`Updated ${affiliateLinks.length} affiliate relationships`);
              }

              return successCount;
            });

            updatedCount += batchSuccess;

            const batchDuration = (Date.now() - batchStartTime) / 1000;
            console.log(
              `Batch ${batchNum} completed in ${batchDuration.toFixed(2)}s: updated ${batchSuccess} ads`
            );
          } catch (error) {
            console.error(`Error in update batch ${batchNum}:`, error);
          }

          // Add a small delay between batches to prevent database overload
          if (i + batchSize < adsToUpdate.length) {
            await new Promise((resolve) => setTimeout(resolve, 100));
          }
        }

        const totalDuration = (Date.now() - startTime) / 1000;
        console.log(
          `Updated ${updatedCount}/${adsToUpdate.length} YouTube ads in ${totalDuration.toFixed(2)}s`
        );
      }

      return {
        success: true,
        message: `Successfully processed YouTube ads: created ${createdCount}, updated ${updatedCount}`,
        data: {
          created: createdCount,
          updated: updatedCount,
          total: response.total,
        },
      };
    } catch (error) {
      console.error('Error processing YouTube ads:', error);
      return {
        success: false,
        message: `Error processing YouTube ads: ${error.message}`,
        error,
      };
    }
  },

  /**
   * Get trending ads by keywords
   * @param {string[]} keywords - Array of keywords to search for
   * @param {object} params - Optional parameters for the search
   * @returns {object} Search results for all keywords
   */
  async getTrendingAdsByKeywords(keywords: string[], params?: any) {
    if (!keywords || !keywords.length) {
      return {
        success: false,
        message: 'No keywords provided',
      };
    }

    const allResults = await Promise.all(
      keywords.map(async (keyword) => {
        const result = await this.searchTrendingAds(keyword, params);
        return {
          keyword,
          ...result,
        };
      })
    );

    return {
      success: true,
      data: allResults,
    };
  },

  /**
   * Get trending ads from the database
   * @param {object} params - Query parameters
   * @returns {array} Trending ads from database
   */
  async getTrendingAds(params?: any) {
    try {
      const filters = params?.filters || {};
      const sort = params?.sort || 'last_fetched:desc';
      const pagination = params?.pagination || { page: 1, pageSize: 25 };

      // Add is_displayed true filter to the query
      const query = {
        filters: {
          ...filters,
          is_displayed: true,
        },
        sort,
        pagination,
      };

      const [results, resultPagination] = await strapi.db
        .query('api::top-ad.top-ad')
        .findWithCount(query);

      return {
        data: results,
        meta: {
          pagination: resultPagination,
        },
      };
    } catch (error) {
      console.error('Error fetching trending ads:', error);
      throw error;
    }
  },

  /**
   * Parse TikTok ad data into a standardized format
   * @param {object} adData - Raw ad data from TikTok API
   * @param {string} keyword - Optional keyword used for the search
   * @returns {ITikTokAd} Parsed ad data
   */
  parseTikTokAd(adData: any, keyword?: string): ITikTokAd {
    return {
      ad_id: adData.id,
      ad_title: adData.ad_title || '',
      brand_name: adData.brand_name || '',
      ctr: typeof adData.ctr === 'number' ? adData.ctr : 0,
      cost: typeof adData.cost === 'number' ? adData.cost : 0,
      industry_key: adData.industry_key || '',
      country_code: Array.isArray(adData.country_code) ? adData.country_code : [],
      landing_page: adData.landing_page || '',
      likes: typeof adData.like === 'number' ? adData.like : 0,
      comments: typeof adData.comment === 'number' ? adData.comment : 0,
      shares: typeof adData.share === 'number' ? adData.share : 0,
      objective_key: adData.objective_key || '',
      objectives: Array.isArray(adData.objectives) ? adData.objectives : [],
      source: adData.source || '',
      video_info: adData.video_info || {},
      is_search: !!adData.is_search,
      favorite: !!adData.favorite,
      keyword: keyword || '',
      platform: 'tiktok',
      is_displayed: false,
      published_from: new Date(Date.now()),
    };
  },

  /**
   * Parse YouTube ad data into a standardized format
   * @param {object} adData - Raw ad data from Adplexity API
   * @param {string} keyword - Optional keyword used for the search
   * @returns {IYoutubeAd} Parsed ad data
   */
  parseYoutubeAd(adData: any, keyword?: string): IYoutubeAd {
    console.log('Parsing YouTube ad with ID:', adData.id);

    // Use youtube_id if available, otherwise use the main id
    const adId = adData.id ? String(adData.id) : String(adData.id || this.generateUniqueId());

    // Extract views - prioritize different fields based on availability
    const views = (() => {
      if (typeof adData.youtube_views_count === 'number') {
        return adData.youtube_views_count;
      } else if (typeof adData.hits_total === 'number') {
        return adData.hits_total;
      } else if (typeof adData.hits === 'number') {
        return adData.hits;
      }
      return 0;
    })();

    // Extract likes from youtube_likes_count if available
    const likes = typeof adData.youtube_likes_count === 'number' ? adData.youtube_likes_count : 0;

    // Get campaign duration from days data
    const daysRunning =
      typeof adData.days_total === 'number'
        ? adData.days_total
        : typeof adData.days === 'number'
          ? adData.days
          : 0;

    // Extract country data - handle array of country codes
    const countries = Array.isArray(adData.countries)
      ? adData.countries.map((country) => (typeof country === 'string' ? country : String(country)))
      : [];

    // Get advertiser name directly from youtube object or other fields
    const advertiserName = (() => {
      if (adData.youtube?.advertiser_name) return adData.youtube.advertiser_name;
      if (adData.youtube_author) return adData.youtube_author;
      if (adData.advertiserName) return adData.advertiserName;
      return '';
    })();

    // Get call-to-action button text
    const ctaButton = adData.youtube?.cta_button || '';

    // Extract image URL with priority order
    const imageUrl = adData.image_url || adData.thumb_url || '';

    // Prefer English title if available, fallback to original
    const title = adData.title_en || adData.title || adData.title_orig || 'Untitled Ad';

    // Prefer English description if available
    const description =
      adData.description_en || adData.description || adData.description_orig || '';

    // Extract ad type code
    const adType = typeof adData.type === 'number' ? adData.type : 0;

    // Create video info object with image dimensions and additional data
    const videoInfo = {
      vid: adId,
      duration: daysRunning,
      cover: imageUrl,
      video_url: { embed_url: `https://www.youtube.com/embed/${adData.youtube_id}?autoplay=1` },
      width: adData.image_sizes?.width || 0,
      height: adData.image_sizes?.height || 0,
      // Store additional fields in video_info JSON
      description: description,
      cta_button: ctaButton,
      networks: Array.isArray(adData.networks) ? adData.networks : [],
      devices: Array.isArray(adData.devices) ? adData.devices : [],
      publishers_count: typeof adData.publishers_count === 'number' ? adData.publishers_count : 0,
      tracking_tools: Array.isArray(adData.tracking_tools) ? adData.tracking_tools : [],
      aff_networks: Array.isArray(adData.aff_networks) ? adData.aff_networks : [],
      first_seen: adData.first_seen || '',
      last_seen: adData.last_seen || '',
      ad_type: adType,
    };

    // Return an object containing only the fields defined in the schema
    return {
      ad_id: adId,
      ad_title: title,
      brand_name: advertiserName,
      ctr: 0, // Not typically available in Adplexity data
      cost: 0, // Not typically available in Adplexity data
      industry_key: '',
      country_code: countries,
      landing_page: '',
      likes: likes,
      comments: 0, // Not typically available in Adplexity data
      shares: 0, // Not typically available in Adplexity data
      objective_key: '',
      objectives: [],
      source: '',
      video_info: videoInfo,
      is_search: true,
      favorite: false,
      keyword: keyword || '',
      platform: 'youtube',
      is_displayed: true,
      views: views,
      // These fields will be stored in video_info instead
      description: description,
      cta_button: ctaButton,
      networks: Array.isArray(adData.networks) ? adData.networks : [],
      devices: Array.isArray(adData.devices) ? adData.devices : [],
      publishers_count: typeof adData.publishers_count === 'number' ? adData.publishers_count : 0,
      tracking_tools: Array.isArray(adData.tracking_tools) ? adData.tracking_tools : [],
      aff_networks: Array.isArray(adData.aff_networks) ? adData.aff_networks : [],
      first_seen: adData.first_seen || '',
      last_seen: adData.last_seen || '',
      published_from: new Date(adData.first_seen || Date.now()),
    };
  },

  /**
   * Generate a unique ID for internal use
   * @param {number} length - Length of the ID to generate
   * @returns {string} Generated unique ID
   */
  generateUniqueId(length = 24) {
    return crypto
      .randomBytes(length)
      .toString('base64')
      .replace(/[^a-z0-9]/gi, '') // Remove non-alphanumeric chars
      .slice(0, length); // Trim to desired length
  },

  /**
   * Rate limiter to control API request rate
   * @param {Function} fn - The function to rate limit
   * @param {number} ratePerSecond - Maximum number of requests per second
   * @returns {Function} Rate-limited function
   */
  rateLimiter(fn, ratePerSecond = 3) {
    const queue = [];
    const requestTimestamps = [];
    let processing = false;

    // Calculate delay needed to maintain rate limit
    const getDelayMs = () => {
      const now = Date.now();

      // Clean up old timestamps (older than 1 second)
      while (requestTimestamps.length > 0 && now - requestTimestamps[0] >= 1000) {
        requestTimestamps.shift();
      }

      // If we haven't hit our rate limit yet, no delay needed
      if (requestTimestamps.length < ratePerSecond) {
        return 0;
      }

      // Calculate when the next slot will be available
      // This is when the oldest request will be more than 1 second old
      const nextAvailableTime = requestTimestamps[0] + 1000;
      const delayMs = Math.max(0, nextAvailableTime - now + 10); // Add 10ms buffer

      return delayMs;
    };

    // Process the queue with proper rate limiting
    const processQueue = async () => {
      if (processing || queue.length === 0) return;

      processing = true;
      console.log(
        `Starting to process queue of ${queue.length} items with rate limit of ${ratePerSecond}/s`
      );

      try {
        while (queue.length > 0) {
          const delayMs = getDelayMs();

          if (delayMs > 0) {
            // Wait until we can process the next request
            console.log(
              `Rate limit: waiting ${delayMs}ms before next request (${requestTimestamps.length}/${ratePerSecond} slots used)`
            );
            await new Promise((resolve) => setTimeout(resolve, delayMs));
          }

          // Process next item in queue
          const { params, resolve, reject } = queue.shift();

          // Record this request timestamp
          const timestamp = Date.now();
          requestTimestamps.push(timestamp);

          try {
            console.log(
              `Executing rate-limited function (${requestTimestamps.length}/${ratePerSecond} slots used)`
            );
            const result = await fn(...params);
            resolve(result);
          } catch (error) {
            console.error('Error in rate-limited function:', error);
            reject(error);
          }
        }
      } finally {
        console.log('Finished processing rate-limited queue');
        processing = false;
      }
    };

    // Return a function that will be rate limited
    return (...args) => {
      return new Promise((resolve, reject) => {
        queue.push({ params: args, resolve, reject });
        console.log(`Added item to rate limit queue (${queue.length} items waiting)`);
        processQueue(); // Start processing if not already running
      });
    };
  },

  /**
   * Get transcript for an ad
   * @param {string} adId - The ad ID to get transcript for
   * @param {number} userId - The user ID making the request
   * @returns {object} The transcript, suggestions, and session ID
   */
  async getAdTranscript(adId: string, userId: number) {
    try {
      // Check if we already have a record for this ad
      const topAd = await strapi.db.query('api::top-ad.top-ad').findOne({
        where: {
          $or: [{ ad_id: adId }],
        },
      });

      if (!topAd) {
        throw new Error('Ad not found');
      }

      // Get AI script prompts for suggestions
      const prompts = await strapi.entityService.findMany('api::prompt.prompt', {
        filters: { type: 'aiscript' },
      });

      // Initialize transcript
      let transcript = topAd.transcript || '';

      // Fetch transcript if we don't have it already
      if (!transcript) {
        transcript = await this.fetchAdTranscript(topAd);

        console.log(`Fetched transcript for ad ${topAd.ad_id}:`, transcript);

        // Update the top-ad record with the transcript
        await strapi.db.query('api::top-ad.top-ad').update({
          where: { id: topAd.id },
          data: { transcript },
        });
      }

      // // Create a new AI script session
      // const session = await strapi.entityService.create('api::aiscript-session.aiscript-session', {
      //   data: {
      //     session_id: `ad-transcript-${topAd.ad_id}-${new Date().toISOString()}`,
      //     session_status: 'active',
      //     start_time: new Date(),
      //     users_permissions_user: userId,
      //   },
      // });

      // // Process with AI service in all cases
      // await strapi.service('api::aiscript.aiscript').processUserScript({
      //   message: transcript,
      //   session,
      //   isUseDefaultPrompt: false,
      // });

      // Return consistent response structure
      return {
        success: true,
        transcript,
        suggestions: prompts,
        // sessionId: session.session_id,
      };
    } catch (err) {
      console.error('Error fetching ad transcript:', err);
      return {
        success: false,
        error: err.message || 'Error fetching transcript',
      };
    }
  },

  /**
   * Fetch transcript for an ad based on platform
   * @param {object} topAd - The top ad object
   * @returns {string} The transcript text
   */
  async fetchAdTranscript(topAd) {
    // Get video info from the ad
    const videoInfo = topAd.video_info
      ? typeof topAd.video_info === 'string'
        ? JSON.parse(topAd.video_info)
        : topAd.video_info
      : {};

    // Get video URL or ID
    let videoId = '';
    const videoUrl = '';

    switch (topAd.platform) {
      case 'youtube':
        // Extract video ID from embed URL
        if (videoInfo.video_url?.embed_url) {
          const embedUrl = videoInfo.video_url.embed_url;
          const match = embedUrl.match(/\/embed\/([^?]+)/);
          if (match && match[1]) {
            videoId = match[1];
          }
        }

        if (!videoId && topAd.ad_id) {
          videoId = topAd.ad_id;
        }

        if (videoId) {
          try {
            const youtubeResult = await YoutubeTranscriptClient.getTranscript(videoId);
            return youtubeResult.transcript || '';
          } catch (error) {
            console.error(`Error fetching YouTube transcript for ${videoId}:`, error);
            throw new Error(`Unable to fetch transcript for this YouTube ad (ID: ${videoId})`);
          }
        } else {
          throw new Error('Could not extract YouTube video ID from ad data');
        }
        break;

      default:
        throw new Error(`Transcript not available for ${topAd.platform} platform`);
    }
  },

  /**
   * Clean up duplicate ad_id entries in the top_ads table
   * This should be run before adding a unique constraint
   * @returns {object} Results of the cleanup operation
   */
  async cleanupDuplicateAds() {
    try {
      const startTime = Date.now();
      console.log('Starting duplicate ad cleanup');

      // Step 1: Find duplicate ad_ids
      const duplicates = await strapi.db.connection.raw(`
        SELECT ad_id, COUNT(*), ARRAY_AGG(id) as ids
        FROM top_ads
        GROUP BY ad_id
        HAVING COUNT(*) > 1
        ORDER BY COUNT(*) DESC
      `);

      const duplicateAdIds = duplicates.rows || [];
      console.log(`Found ${duplicateAdIds.length} ad_ids with duplicates`);

      if (duplicateAdIds.length === 0) {
        return {
          success: true,
          message: 'No duplicate ad_ids found',
          data: { processed: 0 },
        };
      }

      // Step 2: Process duplicates - keep the most recent record for each ad_id
      let processedCount = 0;

      await strapi.db.connection.transaction(async (trx) => {
        for (const duplicate of duplicateAdIds) {
          const { ad_id, ids } = duplicate;

          // Get all records for this ad_id and sort by updated_at
          const records = await strapi.db
            .connection('top_ads')
            .where({ ad_id })
            .orderBy('updated_at', 'desc')
            .select('id', 'updated_at')
            .transacting(trx);

          if (records.length <= 1) continue;

          // Keep the most recent record, delete the rest
          const keepId = records[0].id;
          const deleteIds = records.slice(1).map((r) => r.id);

          console.log(
            `For ad_id ${ad_id}: keeping ${keepId}, deleting ${deleteIds.length} duplicates`
          );

          // Delete relationship records for the duplicates
          await strapi.db
            .connection('top_ads_affiliate_lnk')
            .whereIn('top_ad_id', deleteIds)
            .delete()
            .transacting(trx);

          // Delete the duplicate records
          await strapi.db.connection('top_ads').whereIn('id', deleteIds).delete().transacting(trx);

          processedCount += deleteIds.length;
        }
      });

      const totalDuration = (Date.now() - startTime) / 1000;
      console.log(
        `Completed duplicate cleanup: removed ${processedCount} duplicate records in ${totalDuration.toFixed(2)}s`
      );

      return {
        success: true,
        message: `Successfully removed ${processedCount} duplicate records`,
        data: { processed: processedCount },
      };
    } catch (error) {
      console.error('Error cleaning up duplicate ads:', error);
      return {
        success: false,
        message: `Error cleaning up duplicate ads: ${error.message}`,
        error,
      };
    }
  },

  // ...existing code...
}));
