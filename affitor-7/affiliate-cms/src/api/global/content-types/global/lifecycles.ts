import { errors } from '@strapi/utils';
import { ruleMap } from '../../../../utils/cron';
const { ApplicationError } = errors;

/**
 * Main handler that composes all validation functions
 */
async function afterSave(event) {
  const { result } = event;

  // Update cron job schedule after global config update
  try {
    const cronTasks = strapi.cron.jobs;

    // Update Airtable sync schedule
    const airtableSync = cronTasks.find((task) => task.name === 'airtableSync');
    if (airtableSync) {
      const airtableService = strapi.service('api::airtable.airtable');
      const rule = await airtableService.getAirtableCronSchedule();
      console.log('New airtableSync schedule:', rule);

      // Use the reschedule method to update the job's schedule
      airtableSync.job.reschedule(rule);
      console.log('Successfully rescheduled airtableSync job');
    } else {
      console.error('airtableSync task not found');
    }

    // Update TikTok thumbnail update schedule
    const tiktokThumbnailUpdate = cronTasks.find((task) => task.name === 'tiktokThumbnailUpdate');
    if (tiktokThumbnailUpdate) {
      const socialListeningService = strapi.service('api::social-listening.social-listening');
      const rule = await socialListeningService.getTiktokThumbnailUpdateSchedule();
      console.log('New tiktokThumbnailUpdate schedule:', rule);

      // Use the reschedule method to update the job's schedule
      tiktokThumbnailUpdate.job.reschedule(rule);
      console.log('Successfully rescheduled tiktokThumbnailUpdate job');
    } else {
      console.error('tiktokThumbnailUpdate task not found');
    }

    // Update Fetch Affiliate Ads schedule
    const fetchAffiliateAds = cronTasks.find((task) => task.name === 'fetchAffiliateAds');
    if (fetchAffiliateAds && result.fetch_ads_schedule) {
      // Use the cron utility to get the rule from the schedule
      const rule = ruleMap[result.fetch_ads_schedule] || ruleMap['Daily (midnight)'];

      console.log('New fetchAffiliateAds schedule:', rule);

      // Use the reschedule method to update the job's schedule
      fetchAffiliateAds.job.reschedule(rule);
      console.log('Successfully rescheduled fetchAffiliateAds job');
    } else if (!fetchAffiliateAds) {
      console.error('fetchAffiliateAds task not found');
    }
  } catch (error) {
    console.error('Error updating cron job schedule:', error);
  }
}

export default {
  afterUpdate: afterSave,
};
