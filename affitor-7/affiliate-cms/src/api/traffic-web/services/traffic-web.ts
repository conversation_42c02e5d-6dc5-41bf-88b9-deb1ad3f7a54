/**
 * traffic-web service
 */

import { factories } from '@strapi/strapi';
import { SimilarWebClient } from '../../../utils/request';
import { ITrafficData } from '../interface';

export default factories.createCoreService('api::traffic-web.traffic-web', {
  async getTrafficData(documentId: string) {
    try {
      const affiliate = await strapi.documents('api::affiliate.affiliate').findOne({ documentId });

      console.log('affiliate', affiliate);
      if (!affiliate || !affiliate?.domain)
        throw new Error(`Affiliate ${documentId} is invalid to get traffic data`);
      const currentDate = new Date();
      let currentMonth = currentDate.getMonth();
      let currentYear = currentDate.getFullYear();
      if (currentMonth === 0) {
        currentMonth = 12;
        currentYear -= 1;
      }
      console.log('affiliate.domain', affiliate.domain);
      const trafficData = await getTrafficFromSimilarWeb({
        affiliate,
        month: currentMonth,
        year: currentYear,
      });

      // Return the traffic data after processing
      return trafficData;
    } catch (err: any) {
      console.error('LOG-getTrafficData', err);
      throw err;
    }
  },
});

async function getTrafficFromSimilarWeb({
  affiliate,
  month,
  year,
}: {
  affiliate: any;
  month: number;
  year: number;
}) {
  console.log({
    affiliate,
    month,
    year,
  });
  // Check if the data already exists in the database
  const existingData = await strapi.entityService.findMany('api::traffic-web.traffic-web', {
    filters: {
      url: affiliate?.domain,
      month,
      year,
    },
    limit: 1,
  });

  const existingDataItem = existingData.length > 0 ? existingData[0] : null;
  console.log('LOG-existingDataItem', !!existingDataItem);
  if (existingDataItem) {
    return existingDataItem;
  }

  const data = await SimilarWebClient.getTraffic(affiliate?.domain);
  const parsedData = parseSimilarResponse(data);

  // Update the affiliate's monthly_traffic with the new traffic data
  parsedData.visits && (await updateAffiliateTraffic(affiliate.documentId, parsedData.visits));
  const { month: parsedMonth, year: parsedYear } = parsedData;
  // Check if the data already exists in the database
  const existingDataCheck = await strapi.entityService.findMany('api::traffic-web.traffic-web', {
    filters: {
      url: affiliate?.domain,
      month: parsedMonth,
      year: parsedYear,
    },
    limit: 1,
  });
  const existingDataItemCheck = existingDataCheck.length > 0 ? existingDataCheck[0] : null;
  if (existingDataItemCheck) {
    return existingDataItemCheck;
  }

  const savedData = await strapi.entityService.create('api::traffic-web.traffic-web', {
    data: {
      affiliate: affiliate?.id,
      url: affiliate?.domain,
      ...parsedData,
    },
  });

  return savedData;
}

/**
 * Updates the affiliate's monthly_traffic field with the visit count
 * and publishes the updated affiliate
 */
async function updateAffiliateTraffic(affiliateId: string, visits: number) {
  try {
    console.log(`Updating affiliate ${affiliateId} with monthly_traffic: ${visits}`);

    // Update the affiliate with the monthly_traffic value and publish it
    const updatedAffiliate = await strapi.documents('api::affiliate.affiliate').update({
      documentId: affiliateId,
      data: {
        monthly_traffic: visits,
      },
    });

    // publish the updated affiliate
    await strapi.documents('api::affiliate.affiliate').publish({
      documentId: affiliateId,
      data: {
        monthly_traffic: visits,
      },
    });

    console.log(`Successfully updated affiliate ${affiliateId} with monthly_traffic: ${visits}`);
    return updatedAffiliate;
  } catch (error) {
    console.error(`Failed to update affiliate ${affiliateId} monthly_traffic:`, error);
    // Don't throw the error to avoid disrupting the main flow
  }
}

function parseSimilarResponse(response: any): ITrafficData {
  if (!response || !response.Engagments) {
    throw new Error('Invalid response data');
  }

  const engagements = response.Engagments;
  const monthlyVisit = response.EstimatedMonthlyVisits;
  const monthlyVisitData = monthlyVisit
    ? Object.keys(monthlyVisit).map((key) => {
        return {
          month: key,
          visits: parseInt(monthlyVisit[key], 10),
        };
      })
    : [];

  const avgCpc =
    response.TopKeywords && response.TopKeywords.length > 0
      ? response.TopKeywords.reduce((acc: number, item: any) => {
          return acc + parseFloat(item.Cpc || 0);
        }, 0) / response.TopKeywords.length
      : 0;

  const ranking =
    response.TopKeywords && response.TopKeywords.length > 0
      ? response.TopKeywords.sort((a: any, b: any) => b.Volume - a.Volume).reduce(
          (acc: any, item: any, index: number) => {
            acc[item.Name] = index + 1;
            return acc;
          },
          {}
        )
      : {};

  // Process country data from TopCountryShares
  const totalVisits = parseInt(engagements.Visits, 10) || 0;
  const countryData = (response.TopCountryShares || []).map((ct: any) => {
    // Get country name from Countries mapping if available
    const country =
      (response.Countries || []).find((countryItem: any) => countryItem.Code === ct.CountryCode) ||
      {};

    const percentValue = parseFloat(ct.Value) || 0;
    const countryVisits = Math.round(totalVisits * percentValue);

    return {
      country: country.Country,
      country_code: country.CountryCode,
      url_code: country.UrlCode,
      country_name: country.Name,
      value: percentValue,
      percentage: (percentValue * 100).toFixed(2) + '%',
      visits: countryVisits, // Add the calculated number of visits
    };
  });

  const trafficSources = {
    social: parseFloat(response.TrafficSources?.Social || 0),
    paid_referrals: parseFloat(response.TrafficSources?.PaidReferrals || 0),
    mail: parseFloat(response.TrafficSources?.Mail || 0),
    referrals: parseFloat(response.TrafficSources?.Referrals || 0),
    search: parseFloat(response.TrafficSources?.Search || 0),
    direct: parseFloat(response.TrafficSources?.Direct || 0),
  };

  // Extract rank data
  const globalRank = response.GlobalRank?.Rank ? parseInt(response.GlobalRank.Rank, 10) : undefined;
  const countryRank = response.CountryRank
    ? {
        country: response.CountryRank.Country,
        country_code: response.CountryRank.CountryCode,
        rank: parseInt(response.CountryRank.Rank, 10) || 0,
        country_name:
          (response.Countries || []).find(
            (countryItem: any) => countryItem.Code === response.CountryRank.CountryCode
          )?.Name || '',
      }
    : undefined;
  const categoryRank = response.CategoryRank
    ? {
        rank: parseInt(response.CategoryRank.Rank, 10) || 0,
        category: response.CategoryRank.Category,
      }
    : undefined;

  return {
    bounce_rate: parseFloat(engagements.BounceRate) || 0,
    page_per_visit: parseFloat(engagements.PagePerVisit) || 0,
    visits: parseInt(engagements.Visits, 10) || 0,
    time_on_site: parseFloat(engagements.TimeOnSite) || 0,
    month: parseInt(engagements.Month, 10) || 0,
    year: parseInt(engagements.Year, 10) || 0,
    chart: (monthlyVisitData || []).reduce((acc, item) => {
      acc[item.month] = item.visits || 0;
      return acc;
    }, {}),
    top_keyword: {
      summary: {
        total_keywords: response.TopKeywords?.length || 0,
        traffic_origin_search: parseFloat(response.TrafficSources?.Search || 0) || 0,
        avg_cpc: avgCpc || 0,
      },
      keywords: (response.TopKeywords || []).map((keyword: any) => {
        return {
          name: keyword.Name || '',
          rank: ranking[keyword.Name] || 0,
          traffic: parseInt(keyword.Volume, 10) || 0,
          cpc: parseFloat(keyword.Cpc) || 0,
        };
      }),
    },
    top_countries: countryData,
    traffic_sources: trafficSources,
    global_rank: globalRank,
    country_rank: countryRank,
    category_rank: categoryRank,
  };
}
