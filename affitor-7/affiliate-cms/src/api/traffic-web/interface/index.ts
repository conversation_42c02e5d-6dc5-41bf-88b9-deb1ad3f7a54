export interface ITrafficData {
  bounce_rate: number;
  page_per_visit: number;
  visits: number;
  time_on_site: number;
  month: number;
  year: number;
  chart: {
    [date: string]: number;
  };
  // summary_keywords: any;
  top_keyword: any;
  top_countries: {
    country: string;
    url_code: string;
    country_code: string;
    country_name: string;
    value: number;
    percentage: string;
    visits: number; // Added property for country-specific visit counts
  }[];
  traffic_sources: {
    social: number;
    paid_referrals: number;
    mail: number;
    referrals: number;
    search: number;
    direct: number;
  };
  global_rank?: number;
  country_rank?: {
    country: number;
    country_code: string;
    rank: number;
    country_name: string;
  };
  category_rank?: {
    rank: number;
    category: string;
  };
}
export interface ISummaryKeywords {
  total_keywords: number;
  traffic_origin_search: number;
  avg_cpc: number;
}
export interface ITopKeyword {
  keywords: {
    rank: number;
    name: string;
    traffic: number;
    cpc: number;
  }[];
}
