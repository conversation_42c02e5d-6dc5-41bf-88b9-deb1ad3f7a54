import { IAirtableSource } from './../interface/index';
/**
 * airtable service
 */

import { factories } from '@strapi/strapi';
import axios from 'axios';
import { IAffiliate } from '../../affiliate/interfaces';
import { getGlobalConfigProperty } from '../../../utils/global-config';
import { ruleMap } from '../../../utils/cron';

interface AirtableResponse {
  records: Array<{
    id: string;
    fields: {
      [key: string]: any;
    };
  }>;
  offset?: string;
}

export default factories.createCoreService('api::airtable.airtable', ({ strapi }) => ({
  async getAirtableConfig() {
    // Get API key from .env
    const apiKey = process.env.AIRTABLE_API_KEY;

    if (!apiKey) {
      throw new Error('Airtable API key is not set in environment variables');
    }

    // Get base and table IDs from Global config using our utility function
    const airtable_base_id = await getGlobalConfigProperty('airtable_base_id');
    const airtable_table_id = await getGlobalConfigProperty('airtable_table_id');

    if (!airtable_base_id || !airtable_table_id) {
      throw new Error('Airtable base ID or table ID is not configured in global settings');
    }

    return {
      apiKey,
      baseId: airtable_base_id,
      tableId: airtable_table_id,
    };
  },

  async getAirtableCronSchedule() {
    const defaultRule = '0 0 * * *'; // Default to daily at midnight

    try {
      // Get the cron schedule from global config, or use a default if not set
      const airtable_sync_schedule = await getGlobalConfigProperty('airtable_sync_schedule', null);

      if (!airtable_sync_schedule) {
        strapi.log.info('No airtable_sync_schedule found in global config, using default');
        return defaultRule;
      }

      strapi.log.info(`Using cron schedule from global config: ${airtable_sync_schedule}`);
      return ruleMap[airtable_sync_schedule] || defaultRule;
    } catch (error) {
      strapi.log.error('Error getting Airtable cron schedule:', error);
      return defaultRule;
    }
  },

  async createSyncRecord(status: 'success' | 'fail', message: string, count?: number) {
    // Always create a new record for each sync operation
    const syncData = {
      sync_status: status,
      sync_message: message,
      sync_time: new Date(), // Add the required sync_time field
    };

    if (status === 'success' && count !== undefined) {
      syncData['sync_count'] = count;
      syncData['last_sync_time'] = new Date();
    }

    // Create a new record
    return await strapi.entityService.create('api::airtable.airtable', {
      data: syncData,
    });
  },

  async fetchAirtableData() {
    try {
      const { apiKey, baseId, tableId } = await this.getAirtableConfig();
      console.log('apiKey, baseId, tableId', apiKey, baseId, tableId);

      // Initialize variables for pagination
      let allRecords = [];
      let offset = null;

      // Continue fetching until there's no more offset
      do {
        // Build URL with offset if it exists
        let url = `https://api.airtable.com/v0/${baseId}/${tableId}`;
        if (offset) {
          url += `?offset=${offset}`;
        }

        const response = await axios.get(url, {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
        });

        const data = response.data as AirtableResponse;

        // Add the current batch of records to our collection
        allRecords = [...allRecords, ...(data.records || [])];

        // Update offset for the next iteration
        offset = data.offset;

        // Optional: Add a small delay to avoid rate limits
        if (offset) {
          await new Promise((resolve) => setTimeout(resolve, 200));
        }
      } while (offset);

      // Return a response object with all the collected records
      return {
        records: allRecords,
      };
    } catch (error) {
      strapi.log.error('Error fetching Airtable data:', error);
      throw error;
    }
  },

  /**
   * Convert a plain text string to blocks format for Strapi's blocks field type
   */
  convertStringToBlocks(text: string | null) {
    if (!text) return null;

    // Split the text by paragraphs
    const paragraphs = text.split(/\n+/);

    // Convert each paragraph to a block
    return paragraphs.map((paragraph) => ({
      type: 'paragraph',
      children: [
        {
          type: 'text',
          text: paragraph.trim(),
        },
      ],
    }));
  },

  /**
   * Find or create categories based on names
   * @param categoryNames Array of category names from Airtable
   */
  async findOrCreateCategories(categoryNames: string[]): Promise<number[]> {
    const categoryIds: any[] = [];

    if (!categoryNames || !categoryNames.length) {
      return categoryIds;
    }

    // Process each category name
    for (const categoryName of categoryNames) {
      const name = categoryName.trim();
      if (!name) continue;

      // Try to find existing category
      const existingCategories = await strapi.entityService.findMany('api::category.category', {
        filters: { name },
      });

      if (existingCategories && existingCategories.length > 0) {
        // Use existing category
        categoryIds.push(existingCategories[0].id);
      }
    }

    return categoryIds;
  },

  /**
   * Find or create a user based on email from Airtable submission
   * @param email Submitter's email address
   * @param name Optional submitter's name
   * @returns User ID if found/created, null otherwise
   */
  async findOrCreateUserByEmail(email: string, name?: string): Promise<number | null> {
    if (!email || typeof email !== 'string') {
      return null;
    }

    try {
      // Clean and normalize the email
      const cleanEmail = email.trim().toLowerCase();

      // Check if the user already exists
      const existingUser = await strapi.query('plugin::users-permissions.user').findOne({
        where: { email: cleanEmail },
      });

      if (existingUser) {
        // strapi.log.info(`User with email ${cleanEmail} already exists, using existing user`);
        return existingUser.id;
      }

      // Generate a random password (users can reset it later if needed)
      const password =
        Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

      // Create a new user
      const userData = {
        username: cleanEmail, // Use email as username
        email: cleanEmail,
        password,
        confirmed: true, // Auto-confirm since they've submitted content
        blocked: false,
        role: await strapi
          .query('plugin::users-permissions.role')
          .findOne({ where: { type: 'authenticated' } }),
      };

      // Add the name if available
      if (name && typeof name === 'string') {
        userData['fullname'] = name.trim();
      }

      const newUser = await strapi.plugins['users-permissions'].services.user.add(userData);

      await strapi
        .service('api::user-tracking-request.user-tracking-request')
        .getUserTracking(newUser.id);

      strapi.log.info(`Created new user with email ${cleanEmail} from Airtable submission`);
      return newUser.id;
    } catch (error) {
      strapi.log.error(`Error creating user from Airtable email ${email}:`, error);
      return null;
    }
  },

  mapAirtableFieldsToAffiliate(record: IAirtableSource) {
    // Map fields from Airtable to Affiliate schema properties
    const affiliateData: IAffiliate = {
      name: record.fields['Product Name'] || null,
      contact_information:
        record.fields['Submitter Name'] + record.fields['Submitter Email'] || null,
      // Convert Description string to blocks format
      detail: this.convertStringToBlocks(record.fields['Description']),
      company_name: record.fields['Company Name'] || null,
      tag_line: record.fields['Tag Line'] || null,
      // domain: record.fields['Referral Link'] || null,
      features: record.fields['Features'] || null,
      country: record.fields['Country'] || null,
      pricing: record.fields['Pricing'] || null,
      commission_detail: this.convertStringToBlocks(record.fields['Commission Rate']) || null,

      // review_status: record.fields['Review Status'] || 'Pending',
      // rejection_notes: record.fields['Rejection Notes'] || null,
    };
    return affiliateData;
  },

  /**
   * Process all categories at once, creating missing ones and returning a map of name to ID
   */
  async processAllCategories(allCategoryNames: string[]): Promise<Map<string, number>> {
    // Remove duplicates and empty values
    const uniqueCategories = [...new Set(allCategoryNames.filter((name) => name && name.trim()))];

    if (uniqueCategories.length === 0) {
      return new Map();
    }

    // Get existing categories
    const existingCategories = await strapi.entityService.findMany('api::category.category', {
      filters: {
        name: {
          $in: uniqueCategories,
        },
      },
      fields: ['id', 'name'],
    });

    // Create a map of existing categories by name
    const categoryMap = new Map<string, number>();
    existingCategories?.forEach((cat) => {
      categoryMap.set(cat.name, +cat.id);
    });

    // No longer need to create categories that don't exist
    // This function now just returns the mapping of existing category names to IDs

    return categoryMap;
  },

  async syncAffiliates() {
    try {
      const airtableData = await this.fetchAirtableData();
      const defaultUserRefRate = await getGlobalConfigProperty('default_user_ref_rate', 0.3);
      console.log('defaultUserRefRate', defaultUserRefRate);

      if (!airtableData.records || !airtableData.records.length) {
        await this.createSyncRecord('fail', 'No records found in Airtable', 0);
        strapi.log.info('No records found in Airtable');
        return { success: true, message: 'No records to sync' };
      }

      // Filter out non-approved records
      const approvedRecords = airtableData.records.filter(
        (record) => record.fields['Review Status'] === 'Approved'
      );

      if (approvedRecords.length === 0) {
        // await this.createSyncRecord('success', 'No approved records to sync', 0);
        return { success: true, message: 'No approved records to sync' };
      }

      // Collect all category names from all records
      const allCategoryNames = approvedRecords
        .filter((record) => record.fields['Category'] && record.fields['Category'].length)
        .flatMap((record) => record.fields['Category']);

      // Process all categories at once
      const categoryMap = await this.processAllCategories(allCategoryNames);

      // Find all existing affiliates that match our records from the component
      const airtableIds = approvedRecords.map((record) => record.id);
      const existingAffiliates = await strapi.entityService.findMany('api::affiliate.affiliate', {
        filters: {
          airtable_data: {
            airtable_id: {
              $in: airtableIds,
            },
          },
        },
        populate: ['airtable_data', 'user_submitted'],
      });

      // Create a map for quick lookup
      const existingAffiliateMap = new Map();
      existingAffiliates?.forEach((affiliate: any) => {
        if (affiliate.airtable_data && affiliate.airtable_data.airtable_id) {
          existingAffiliateMap.set(affiliate.airtable_data.airtable_id, affiliate);
        }
      });

      // Prepare data for bulk creation and update
      const toCreate = [];
      const toUpdate = [];

      for (const record of approvedRecords) {
        const affiliateData = this.mapAirtableFieldsToAffiliate(record as IAirtableSource);

        // Extract submitter email for user creation
        const submitterEmail = record.fields['Submitter Email'];
        const submitterName = record.fields['Submitter Name'];

        // Find or create user based on submitter email
        let userId = null;
        if (submitterEmail) {
          userId = await this.findOrCreateUserByEmail(submitterEmail, submitterName);
        }

        // Generate slug if needed
        if (!affiliateData.slug && affiliateData.name) {
          affiliateData.slug = affiliateData.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '');
        }

        // Find categories for this record
        const categoryIds =
          record.fields['Category']
            ?.filter((name) => categoryMap.has(name))
            .map((name) => categoryMap.get(name)) || [];

        // Add publishedAt for new records
        affiliateData.publishedAt = null;

        // Set from_airtable to true for all records coming from Airtable
        affiliateData.from_airtable = true;

        // Create airtable_data component
        const airtable_data = {
          airtable_id: record.id,
          user_ref_rate: defaultUserRefRate,
          user_ref_link: record.fields['Referral Link'] || null,
        };

        // Create base data object with or without user link
        const baseData: any = {
          ...affiliateData,
          categories: categoryIds,
          airtable_data,
        };

        // Add user relation if a user was found or created
        if (userId) {
          baseData.user_submitted = userId;
        }

        // Check if this affiliate already exists based on airtable_id
        if (existingAffiliateMap.has(record.id)) {
          const existingAffiliate = existingAffiliateMap.get(record.id);
          const affiliateId = existingAffiliate.id;

          // Check if user_submitted is missing but we have a valid userId
          const needsUserUpdate =
            userId &&
            (!existingAffiliate.user_submitted || existingAffiliate.user_submitted.id !== userId);

          if (needsUserUpdate) {
            console.log(
              `Updating user_submitted for affiliate ${existingAffiliate.id} to user ${userId}`
            );

            // We only update the user_submitted field if needed
            toUpdate.push({
              id: affiliateId,
              data: {
                user_submitted: userId,
              },
            });
          }

          // For other updates, we still use the full data object
          // Only push if not already in toUpdate array
          // if (!needsUserUpdate) {
          //   toUpdate.push({
          //     id: affiliateId,
          //     data: baseData,
          //   });
          // }
        } else {
          console.log('Creating new affiliate:', record.id);
          console.log('Affiliate data:', affiliateData);
          // Create new
          toCreate.push(baseData);
        }
      }

      // Perform bulk operations
      let created = 0;
      let updated = 0;

      // Bulk create
      if (toCreate.length > 0) {
        const createdResults = await Promise.all(
          toCreate.map((data) => strapi.entityService.create('api::affiliate.affiliate', { data }))
        );
        created = createdResults.length;
      }

      // Bulk update - now actually perform updates
      if (toUpdate.length > 0) {
        const updatedResults = await Promise.all(
          toUpdate.map(({ id, data }) =>
            strapi.entityService.update('api::affiliate.affiliate', id, { data })
          )
        );
        updated = updatedResults.length;
      }

      const skipped = airtableData.records.length - (created + updated);
      const totalCount = created + updated;
      const successMessage = `Processed ${airtableData.records.length} records. Created: ${created}, Updated: ${updated}, Skipped: ${skipped}`;

      if (created > 0 || updated > 0) {
        // Create a record for the successful sync
        await this.createSyncRecord('success', successMessage, totalCount);
      }

      return {
        success: true,
        message: successMessage,
        details: {
          total: totalCount,
          created,
          updated,
          skipped,
        },
      };
    } catch (error) {
      console.log(error);
      // Create a record for the error
      await this.createSyncRecord('fail', `Error syncing affiliates: ${error.message}`);
      strapi.log.error('Error syncing Airtable to affiliates:', error.details);
      return {
        success: false,
        message: `Error syncing affiliates: ${error.message}`,
      };
    }
  },

  async validateWebhook(request) {
    // In a production environment, you would implement proper validation
    // This could include verifying signatures or tokens from Airtable
    return true;
  },
}));
