/**
 * payout service
 */

import { factories } from '@strapi/strapi';
import { errors } from '@strapi/utils';

const { ApplicationError } = errors;

export default factories.createCoreService('api::payout.payout', ({ strapi }) => ({
  /**
   * Get the commission configuration including minimum payout setting
   */
  async getCommissionConfig() {
    try {
      // Use Document Service API for Strapi v5 - commission-config is a single type
      const commissionConfig = await strapi
        .documents('api::commission-config.commission-config')
        .findFirst({
          status: 'published',
        });

      if (!commissionConfig) {
        throw new ApplicationError('Commission configuration not found');
      }

      return commissionConfig;
    } catch (error) {
      strapi.log.error('Error fetching commission configuration:', error);
      throw new ApplicationError('Failed to retrieve commission configuration');
    }
  },

  /**
   * Validate payout amount against minimum payout threshold
   */
  async validatePayoutAmount(amount: number): Promise<void> {
    try {
      // Parse the amount to ensure it's a valid number
      const payoutAmount = parseFloat(amount?.toString() || '0');

      if (isNaN(payoutAmount) || payoutAmount <= 0) {
        throw new ApplicationError('Payout amount must be a valid positive number');
      }

      // Get commission configuration
      const commissionConfig = await this.getCommissionConfig();

      // Get minimum payout threshold (note: field name has typo 'minimum_payout')
      const minimumPayout = parseFloat(commissionConfig.minimum_payout?.toString() || '50');

      // Validate against minimum threshold
      if (payoutAmount < minimumPayout) {
        throw new ApplicationError(
          `Payout amount ($${payoutAmount.toFixed(2)}) is below the minimum payout threshold of $${minimumPayout.toFixed(2)}`
        );
      }

      strapi.log.info(
        `Payout amount validation passed: $${payoutAmount.toFixed(2)} meets minimum threshold of $${minimumPayout.toFixed(2)}`
      );
    } catch (error) {
      strapi.log.error('Error validating payout amount:', error);

      // If it's already an ApplicationError, re-throw it
      if (error instanceof ApplicationError) {
        throw error;
      }

      // Otherwise, wrap it in an ApplicationError
      throw new ApplicationError('Failed to validate payout amount');
    }
  },

  /**
   * Validate payout data including amount and referrer balance
   */
  async validatePayoutCreation(data: any): Promise<void> {
    try {
      // Validate required fields
      if (!data.referrer || !data.amount) {
        throw new ApplicationError('Referrer and amount are required for payout creation');
      }

      // Validate payout amount against minimum threshold
      await this.validatePayoutAmount(data.amount);

      // Extract referrer ID - handle different formats (relation object, connect object, or direct ID)
      let referrerId: string | number;
      if (typeof data.referrer === 'object') {
        if (data.referrer.connect && data.referrer.connect.length > 0) {
          // Handle connect format: { connect: [{ id: "123" }] }
          referrerId = data.referrer.connect[0].id || data.referrer.connect[0].documentId;
        } else if (data.referrer.id) {
          // Handle direct object format: { id: "123" }
          referrerId = data.referrer.id;
        } else if (data.referrer.documentId) {
          // Handle document format: { documentId: "123" }
          referrerId = data.referrer.documentId;
        } else {
          throw new ApplicationError('Invalid referrer format in payout data');
        }
      } else {
        // Handle direct ID format
        referrerId = data.referrer;
      }

      if (!referrerId) {
        throw new ApplicationError('Referrer ID could not be extracted from payout data');
      }

      // Get the referrer with their current balance using Entity Service API for better compatibility
      const referrer = await strapi.entityService.findOne('api::referrer.referrer', referrerId, {
        fields: ['balance'],
      });

      if (!referrer) {
        throw new ApplicationError('Referrer not found');
      }

      // Parse amounts
      const payoutAmount = parseFloat(data.amount?.toString() || '0');
      const referrerBalance = parseFloat(referrer.balance?.toString() || '0') || 0;

      // Check if referrer has sufficient balance
      if (referrerBalance < payoutAmount) {
        throw new ApplicationError(
          `Insufficient balance. Referrer balance: $${referrerBalance.toFixed(2)}, Payout amount: $${payoutAmount.toFixed(2)}`
        );
      }

      strapi.log.info(
        `Payout creation validation passed: Referrer ${referrerId} has sufficient balance ($${referrerBalance.toFixed(2)}) for payout of $${payoutAmount.toFixed(2)}`
      );
    } catch (error) {
      strapi.log.error('Error validating payout creation:', error);

      // If it's already an ApplicationError, re-throw it
      if (error instanceof ApplicationError) {
        throw error;
      }

      // Otherwise, wrap it in an ApplicationError
      throw new ApplicationError('Failed to validate payout creation');
    }
  },

  /**
   * Validate payout amount update
   */
  async validatePayoutUpdate(payoutId: number, data: any): Promise<void> {
    try {
      // Only validate amount if it's being updated
      if (data.amount !== undefined) {
        await this.validatePayoutAmount(data.amount);

        // If amount is being updated, also check referrer balance
        if (data.referrer || payoutId) {
          // Get the payout with populated referrer using Entity Service API for better compatibility
          const payout = (await strapi.entityService.findOne('api::payout.payout', payoutId, {
            populate: {
              referrer: {
                fields: ['balance'],
              },
            },
          })) as any;

          if (!payout || !payout.referrer) {
            throw new ApplicationError('Payout or referrer not found');
          }

          const newPayoutAmount = parseFloat(data.amount?.toString() || '0');
          const referrerBalance = parseFloat(payout.referrer.balance?.toString() || '0') || 0;

          // Check if referrer has sufficient balance for the new amount
          if (referrerBalance < newPayoutAmount) {
            throw new ApplicationError(
              `Insufficient balance for updated amount. Referrer balance: $${referrerBalance.toFixed(2)}, New payout amount: $${newPayoutAmount.toFixed(2)}`
            );
          }
        }
      }
    } catch (error) {
      strapi.log.error('Error validating payout update:', error);

      // If it's already an ApplicationError, re-throw it
      if (error instanceof ApplicationError) {
        throw error;
      }

      // Otherwise, wrap it in an ApplicationError
      throw new ApplicationError('Failed to validate payout update');
    }
  },
}));
