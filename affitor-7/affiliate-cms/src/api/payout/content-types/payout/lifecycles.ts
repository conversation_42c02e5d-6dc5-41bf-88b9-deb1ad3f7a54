import { errors } from '@strapi/utils';
const { ApplicationError } = errors;

interface PayoutWithReferrer {
  id: number;
  amount: number;
  payout_status: string;
  referrer: {
    id: number;
    balance: number;
  };
}

/**
 * Before creating a payout, validate amount against minimum threshold and check referrer balance
 */
async function beforeCreate(event) {
  const { data } = event.params;

  try {
    // Use the payout service for comprehensive validation including minimum payout threshold
    const payoutService = strapi.service('api::payout.payout') as any;
    await payoutService.validatePayoutCreation(data);
  } catch (error) {
    strapi.log.error('Error in payout beforeCreate validation:', error);

    // If it's already an ApplicationError, re-throw it
    if (error instanceof ApplicationError) {
      throw error;
    }

    // Otherwise, wrap it in an ApplicationError
    throw new ApplicationError(error.message || 'Error validating payout creation');
  }
}

/**
 * Before updating a payout, validate amount changes against minimum threshold
 */
async function beforeUpdate(event) {
  const { data, where } = event.params;

  try {
    // Only validate if amount is being updated
    if (data.amount !== undefined) {
      const payoutService = strapi.service('api::payout.payout') as any;
      await payoutService.validatePayoutUpdate(where.id, data);
    }
  } catch (error) {
    strapi.log.error('Error in payout beforeUpdate validation:', error);

    // If it's already an ApplicationError, re-throw it
    if (error instanceof ApplicationError) {
      throw error;
    }

    // Otherwise, wrap it in an ApplicationError
    throw new ApplicationError(error.message || 'Error validating payout update');
  }
}

/**
 * After updating a payout, check if status changed to completed and deduct balance
 */
async function afterUpdate(event) {
  const { data, where } = event.params;

  try {
    // Only proceed if payout_status was updated to 'completed'
    if (data.payout_status !== 'completed') {
      return;
    }

    // Get the current payout with referrer information
    const payout = (await strapi.entityService.findOne('api::payout.payout', where.id, {
      fields: ['amount', 'payout_status'],
      populate: {
        referrer: {
          fields: ['balance'],
        },
      },
    })) as unknown as PayoutWithReferrer;

    if (!payout || !payout.referrer) {
      console.error('Payout or referrer not found for balance deduction');
      return;
    }

    // Parse amounts
    const payoutAmount = parseFloat(payout.amount?.toString() || '0') || 0;
    const currentBalance = parseFloat(payout.referrer.balance?.toString() || '0') || 0;
    const newBalance = currentBalance - payoutAmount;

    // Ensure balance doesn't go negative (additional safety check)
    if (newBalance < 0) {
      console.error(
        `Warning: Payout completion would result in negative balance. Current: $${currentBalance.toFixed(2)}, Payout: $${payoutAmount.toFixed(2)}`
      );
      // You might want to throw an error here or handle this case differently
      // For now, we'll proceed but log the warning
    }

    // Update the referrer's balance
    await strapi.entityService.update('api::referrer.referrer', payout.referrer.id, {
      data: {
        balance: newBalance,
      },
    });

    console.log(
      `Payout completed: Deducted $${payoutAmount.toFixed(2)} from referrer ${payout.referrer.id}. New balance: $${newBalance.toFixed(2)}`
    );
  } catch (error) {
    console.error('Error in payout afterUpdate balance deduction:', error);
    // Note: We don't throw errors in afterUpdate to avoid rolling back the payout status change
    // The payout status update should succeed even if balance deduction fails
    // You might want to implement a retry mechanism or manual intervention process
  }
}

export default {
  beforeCreate,
  beforeUpdate,
  afterUpdate,
};
