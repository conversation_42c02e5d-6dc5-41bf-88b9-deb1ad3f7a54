export default {
  routes: [
    {
      method: 'GET',
      path: '/discourse/config',
      handler: 'discourse.getConfig',
      config: {
        auth: false, // Public endpoint
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/discourse/sso',
      handler: 'discourse.handleInitialSSORequest',
      config: {
        auth: false, // Discourse calls this endpoint
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/discourse/login-url',
      handler: 'discourse.getLoginUrl',
      config: {
        auth: false, // Public endpoint
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/discourse/sso-url',
      handler: 'discourse.getSSOUrl',
      config: {
        auth: {
          scope: [],
        },
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/discourse/sso-url',
      handler: 'discourse.getSSOUrl',
      config: {
        auth: {
          scope: [],
        },
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/discourse/sso-return',
      handler: 'discourse.handleSSOReturn',
      config: {
        auth: false, // Discourse calls this endpoint
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/discourse/sso-logout',
      handler: 'discourse.handleSSOLogout',
      config: {
        auth: {
          scope: [],
        },
        policies: [],
        middlewares: [],
      },
    },
    // {
    //   method: 'POST',
    //   path: '/discourse/sync-user',
    //   handler: 'discourse.syncUser',
    //   config: {
    //     auth: {
    //       scope: [],
    //     },
    //     policies: [],
    //     middlewares: [],
    //   },
    // },
  ],
};
