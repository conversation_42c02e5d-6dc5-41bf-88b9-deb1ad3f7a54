{"kind": "collectionType", "collectionName": "subscription_tiers", "info": {"singularName": "subscription-tier", "pluralName": "subscription-tiers", "displayName": "Subscription Tier", "description": "Define different subscription tiers with pricing and features"}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "display_name": {"type": "string", "required": true}, "description": {"type": "text"}, "price": {"type": "decimal", "required": true, "min": 0}, "save_percent": {"type": "decimal", "min": 0}, "request_limit": {"type": "integer", "required": true, "min": 0, "default": 100}, "duration_days": {"type": "integer", "required": true, "min": 1, "default": 30}, "features": {"type": "json", "description": "List of features included in this tier"}, "is_popular": {"type": "boolean", "default": false}, "transactions": {"type": "relation", "relation": "oneToMany", "target": "api::transaction.transaction", "mappedBy": "subscription_tier"}, "stripe_product_id": {"type": "string"}, "stripe_price_id": {"type": "string"}, "stripe_recurring_interval": {"type": "enumeration", "enum": ["month", "year", "quarter"], "default": "month"}, "stripe_metadata": {"type": "json"}, "is_active": {"type": "boolean", "default": true}, "tier_features": {"displayName": "listing", "type": "component", "repeatable": true, "component": "shared.listing"}, "traffic_share_rate": {"type": "decimal"}, "users": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user", "mappedBy": "subscription_tier"}}}