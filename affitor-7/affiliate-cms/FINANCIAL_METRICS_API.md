# Financial Metrics Recalculation API

This document describes the new API endpoints for calculating and updating aggregate financial metrics for referrers in the affiliate system.

## Overview

The Financial Metrics API provides endpoints to recalculate and synchronize the `total_revenue` and `total_earnings` fields for referrers based on their actual commission data. This ensures data consistency and provides administrative tools for maintenance.

## Endpoints

### 1. Bulk Recalculate Metrics

**POST** `/api/referrers/recalculate-metrics`

Recalculates financial metrics for all referrers or a filtered set of referrers.

#### Request Body (Optional)
```json
{
  "dateFrom": "2024-01-01T00:00:00.000Z",
  "dateTo": "2024-12-31T23:59:59.999Z",
  "referrerIds": [1, 2, 3]
}
```

#### Parameters
- `dateFrom` (optional): ISO date string - Start date for filtering commissions
- `dateTo` (optional): ISO date string - End date for filtering commissions  
- `referrerIds` (optional): Array of referrer IDs to process (if not provided, processes all referrers)

#### Response
```json
{
  "success": true,
  "message": "Bulk recalculation completed. Successfully processed 15/16 referrers.",
  "data": {
    "processed": 15,
    "errors": [
      {
        "referrerId": 16,
        "error": "Referrer with ID 16 not found",
        "timestamp": "2024-01-15T10:30:00.000Z"
      }
    ],
    "summary": {
      "totalReferrers": 16,
      "successfulUpdates": 15,
      "failedUpdates": 1,
      "totalRevenueUpdated": 125430.50,
      "totalEarningsUpdated": 12543.05
    },
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

### 2. Single Referrer Recalculation

**POST** `/api/referrers/:id/sync-totals`

Recalculates financial metrics for a specific referrer.

#### URL Parameters
- `id`: Referrer ID

#### Request Body (Optional)
```json
{
  "dateFrom": "2024-01-01T00:00:00.000Z",
  "dateTo": "2024-12-31T23:59:59.999Z"
}
```

#### Response
```json
{
  "success": true,
  "message": "Financial metrics recalculated successfully for referrer 123",
  "data": {
    "success": true,
    "referrerId": 123,
    "previousTotals": {
      "total_revenue": 5000.00,
      "total_earnings": 500.00
    },
    "updatedTotals": {
      "total_revenue": 5250.75,
      "total_earnings": 525.08
    },
    "calculationDetails": {
      "commissionsProcessed": 25,
      "dateRange": {
        "from": "2024-01-01T00:00:00.000Z",
        "to": "2024-12-31T23:59:59.999Z"
      }
    },
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

### 3. Metrics Summary

**GET** `/api/referrers/:id/metrics-summary`

Retrieves detailed financial metrics summary for a referrer.

#### URL Parameters
- `id`: Referrer ID

#### Query Parameters (Optional)
- `dateFrom`: ISO date string - Start date for filtering
- `dateTo`: ISO date string - End date for filtering

#### Response
```json
{
  "success": true,
  "data": {
    "referrer": {
      "id": 123,
      "username": "john_doe",
      "email": "<EMAIL>",
      "currentTotals": {
        "total_revenue": 5250.75,
        "total_earnings": 525.08,
        "balance": 425.08
      }
    },
    "metrics": {
      "totalCommissions": 25,
      "statusBreakdown": {
        "pending": 5,
        "ready": 15,
        "paid": 5
      },
      "financialSummary": {
        "totalRevenue": 5250.75,
        "totalEarnings": 525.08,
        "averageCommissionRate": 10.0
      },
      "dateRange": {
        "from": "2024-01-01T00:00:00.000Z",
        "to": "2024-12-31T23:59:59.999Z"
      }
    },
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

## Calculation Logic

### Total Revenue
Calculated by summing all `gross_sale_amount` values from related referral commissions.

### Total Earnings  
Calculated by summing all `commission_amount` values from related referral commissions.

### Data Validation
- All monetary values are rounded to 2 decimal places
- Invalid date formats return appropriate error messages
- Non-existent referrer IDs are handled gracefully

## Use Cases

### 1. Data Migration
When migrating existing data or after schema changes:
```bash
curl -X POST http://localhost:1337/api/referrers/recalculate-metrics \
  -H "Content-Type: application/json"
```

### 2. Single Referrer Sync
After manual data corrections for a specific referrer:
```bash
curl -X POST http://localhost:1337/api/referrers/123/sync-totals \
  -H "Content-Type: application/json"
```

### 3. Periodic Maintenance
Recalculate metrics for a specific time period:
```bash
curl -X POST http://localhost:1337/api/referrers/recalculate-metrics \
  -H "Content-Type: application/json" \
  -d '{"dateFrom": "2024-01-01", "dateTo": "2024-01-31"}'
```

### 4. Administrative Reporting
Get detailed metrics for analysis:
```bash
curl -X GET "http://localhost:1337/api/referrers/123/metrics-summary?dateFrom=2024-01-01&dateTo=2024-01-31"
```

## Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
  "error": {
    "status": 400,
    "name": "BadRequestError",
    "message": "Invalid dateFrom format. Use ISO date string",
    "details": {}
  }
}
```

#### 404 Not Found
```json
{
  "error": {
    "status": 404,
    "name": "NotFoundError", 
    "message": "Referrer with ID 123 not found",
    "details": {}
  }
}
```

## Performance Considerations

- Bulk operations process referrers sequentially to avoid database overload
- Large datasets may take several minutes to process
- Consider running bulk operations during low-traffic periods
- The API includes detailed logging for monitoring progress

## Security

- These endpoints should be restricted to administrative users
- Consider adding authentication policies in the route configuration
- Audit logging is automatically handled through Strapi's logging system
